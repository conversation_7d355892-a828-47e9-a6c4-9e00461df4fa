#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版桑基图生成器
为科学问题C1生成高质量、优雅的桑基图可视化
"""

import plotly.graph_objects as go
from plotly.offline import plot
import plotly.express as px
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedSankeyGenerator:
    def __init__(self):
        # 科学问题C1的准确数据
        self.scientific_problem = {
            'id': 'C1',
            'title': '面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究',
            'patents': [
                {
                    'title': '一种油气物联网的管理系统、管理方法、电子设备及介质',
                    'inventors': '刘星; 卫乾; 赵亮; 孙永光',
                    'application_date': '2025',
                    'publication_date': '2025-06-20',
                    'relevance': 'Focuses on IoT management system with data fusion and transmission optimization, relevant to dynamic optimization mechanisms',
                    'strength': '强相关',
                    'score': 8.5
                },
                {
                    'title': '一种面向油气生产物联网控制系统的自组态建模方法',
                    'inventors': '卫乾; 孙红军; 张明; 魏靖雯',
                    'application_date': '2024',
                    'publication_date': '2024-07-16',
                    'relevance': 'Proposes self-configuration modeling for IoT control systems, aligning with dynamic optimization',
                    'strength': '强相关',
                    'score': 8.0
                },
                {
                    'title': '一种知识融合的方法和装置',
                    'inventors': '田芷瑜; 杨勇; 卫乾; 赵世亮; 许野; 刘斌; 江捷; 徐濮; 王雨婷; 李太帆',
                    'application_date': '2024',
                    'publication_date': '2024-10-15',
                    'relevance': 'Involves knowledge fusion techniques, relevant to multi-modal data integration',
                    'strength': '中等相关',
                    'score': 6.5
                },
                {
                    'title': '问答搜索引擎的构建方法和查询业务数据的方法',
                    'inventors': '田芷瑜; 杨勇; 卫乾; 周丽; 吕彬; 李长卓',
                    'application_date': '2024',
                    'publication_date': '2024-06-21',
                    'relevance': 'Builds a Q&A search engine for business data, relevant to language model applications',
                    'strength': '中等相关',
                    'score': 6.0
                },
                {
                    'title': '一种用于智能问答的文本解析方法和装置',
                    'inventors': '吕彬; 杨勇; 卫乾; 汪韶雷',
                    'application_date': '2024',
                    'publication_date': '2024-04-12',
                    'relevance': 'Text parsing for intelligent Q&A, directly relevant to language model applications',
                    'strength': '中等相关',
                    'score': 5.5
                }
            ]
        }
        
        # 关联强度配置
        self.strength_config = {
            '强相关': {'color': 'rgba(255, 99, 132, 0.8)', 'weight': 3, 'node_color': '#FF6384'},
            '中等相关': {'color': 'rgba(54, 162, 235, 0.8)', 'weight': 2, 'node_color': '#36A2EB'},
            '弱相关': {'color': 'rgba(255, 206, 86, 0.8)', 'weight': 1, 'node_color': '#FFCE56'}
        }
    
    def create_enhanced_sankey(self):
        """创建增强版桑基图"""
        patents = self.scientific_problem['patents']
        problem_title = self.scientific_problem['title']
        
        # 准备节点数据
        node_labels = []
        node_colors = []
        
        # 添加专利节点（左侧）
        for patent in patents:
            # 简化专利标题显示
            title = patent['title']
            if len(title) > 25:
                title = title[:22] + "..."
            node_labels.append(title)
            
            # 根据关联强度设置节点颜色
            strength = patent['strength']
            node_colors.append(self.strength_config[strength]['node_color'])
        
        # 添加科学问题节点（右侧）
        problem_display = "多模态大语言模型\n协同架构与动态优化"
        node_labels.append(problem_display)
        node_colors.append('#2E8B57')  # 深绿色
        
        # 准备连接数据
        source_indices = list(range(len(patents)))
        target_indices = [len(patents)] * len(patents)
        values = [self.strength_config[patent['strength']]['weight'] for patent in patents]
        link_colors = [self.strength_config[patent['strength']]['color'] for patent in patents]
        
        # 创建桑基图
        fig = go.Figure(data=[go.Sankey(
            arrangement="snap",
            node=dict(
                pad=20,
                thickness=25,
                line=dict(color="rgba(0,0,0,0.3)", width=1),
                label=node_labels,
                color=node_colors,
                x=[0.1] * len(patents) + [0.9],  # 专利在左侧，科学问题在右侧
                y=[i/(len(patents)-1) for i in range(len(patents))] + [0.5]  # 垂直分布
            ),
            link=dict(
                source=source_indices,
                target=target_indices,
                value=values,
                color=link_colors,
                hovertemplate='<b>%{source.label}</b><br>' +
                             '→ <b>%{target.label}</b><br>' +
                             '关联强度: %{customdata[0]}<br>' +
                             '关联分数: %{customdata[1]}<br>' +
                             '发明人: %{customdata[2]}<br>' +
                             '发布日期: %{customdata[3]}<extra></extra>',
                customdata=[[patent['strength'], patent['score'], 
                           patent['inventors'], patent['publication_date']] 
                          for patent in patents]
            )
        )])
        
        # 设置图表布局
        fig.update_layout(
            title={
                'text': f"<b>专利技术支撑关系分析</b><br><span style='font-size:14px;color:#666'>{problem_title}</span>",
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 18, 'family': 'Microsoft YaHei, Arial, sans-serif'}
            },
            font=dict(size=11, family='Microsoft YaHei, Arial, sans-serif'),
            width=1400,
            height=700,
            margin=dict(l=20, r=20, t=120, b=50),
            paper_bgcolor='rgba(248,249,250,1)',
            plot_bgcolor='rgba(255,255,255,1)'
        )
        
        return fig
    
    def generate_enhanced_report(self):
        """生成增强版分析报告"""
        patents = self.scientific_problem['patents']
        problem_title = self.scientific_problem['title']
        
        # 统计信息
        total_patents = len(patents)
        strong_count = sum(1 for p in patents if p['strength'] == '强相关')
        medium_count = sum(1 for p in patents if p['strength'] == '中等相关')
        weak_count = sum(1 for p in patents if p['strength'] == '弱相关')
        avg_score = sum(p['score'] for p in patents) / total_patents
        
        report_html = f"""
        <div class="analysis-container">
            <div class="header-section">
                <h1 class="main-title">专利技术支撑关系分析报告</h1>
                <div class="problem-card">
                    <h2 class="problem-title">{problem_title}</h2>
                    <div class="problem-id">科学问题编号: C1</div>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card total">
                    <div class="stat-number">{total_patents}</div>
                    <div class="stat-label">相关专利总数</div>
                </div>
                <div class="stat-card strong">
                    <div class="stat-number">{strong_count}</div>
                    <div class="stat-label">强相关专利</div>
                </div>
                <div class="stat-card medium">
                    <div class="stat-number">{medium_count}</div>
                    <div class="stat-label">中等相关专利</div>
                </div>
                <div class="stat-card weak">
                    <div class="stat-number">{weak_count}</div>
                    <div class="stat-label">弱相关专利</div>
                </div>
            </div>
            
            <div class="methodology-section">
                <h3>评估方法说明</h3>
                <div class="method-grid">
                    <div class="method-card strong-method">
                        <div class="method-header">
                            <span class="strength-indicator strong"></span>
                            <span class="method-title">强相关</span>
                        </div>
                        <div class="method-desc">直接支撑科学问题核心技术，在物联网架构、多模态数据处理或动态优化方面有重要贡献</div>
                    </div>
                    <div class="method-card medium-method">
                        <div class="method-header">
                            <span class="strength-indicator medium"></span>
                            <span class="method-title">中等相关</span>
                        </div>
                        <div class="method-desc">在相关技术领域有贡献，如知识融合、智能问答等，间接支撑科学问题</div>
                    </div>
                    <div class="method-card weak-method">
                        <div class="method-header">
                            <span class="strength-indicator weak"></span>
                            <span class="method-title">弱相关</span>
                        </div>
                        <div class="method-desc">在基础技术或外围技术方面有关联，为科学问题提供技术基础</div>
                    </div>
                </div>
            </div>
            
            <div class="patents-section">
                <h3>专利详细信息</h3>
                <div class="patents-list">
        """
        
        # 添加专利详细信息
        for i, patent in enumerate(patents, 1):
            strength_class = patent['strength'].replace('相关', '').lower()
            if strength_class == '强': strength_class = 'strong'
            elif strength_class == '中等': strength_class = 'medium'
            elif strength_class == '弱': strength_class = 'weak'
            
            report_html += f"""
                    <div class="patent-card {strength_class}">
                        <div class="patent-header">
                            <span class="patent-number">{i}</span>
                            <span class="strength-badge {strength_class}">{patent['strength']}</span>
                            <span class="score-badge">评分: {patent['score']}</span>
                        </div>
                        <h4 class="patent-title">{patent['title']}</h4>
                        <div class="patent-info">
                            <div class="info-item">
                                <strong>发明人:</strong> {patent['inventors']}
                            </div>
                            <div class="info-item">
                                <strong>发布日期:</strong> {patent['publication_date']}
                            </div>
                            <div class="info-item">
                                <strong>技术关联:</strong> {patent['relevance']}
                            </div>
                        </div>
                    </div>
            """
        
        report_html += f"""
                </div>
            </div>
            
            <div class="footer-section">
                <div class="generation-info">
                    <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                    <p><strong>平均关联分数:</strong> {avg_score:.1f}</p>
                </div>
            </div>
        </div>
        """
        
        return report_html

    def get_enhanced_css(self):
        """获取增强版CSS样式"""
        return """
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .analysis-container {
                max-width: 1400px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                backdrop-filter: blur(10px);
            }

            .header-section {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }

            .main-title {
                font-size: 2.5em;
                font-weight: 300;
                margin-bottom: 20px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .problem-card {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 25px;
                margin-top: 20px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .problem-title {
                font-size: 1.3em;
                font-weight: 400;
                line-height: 1.6;
                margin-bottom: 10px;
            }

            .problem-id {
                font-size: 0.9em;
                opacity: 0.8;
                font-weight: 300;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                padding: 40px;
                background: #f8f9fa;
            }

            .stat-card {
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                border-left: 5px solid;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            }

            .stat-card.total { border-left-color: #3498db; }
            .stat-card.strong { border-left-color: #e74c3c; }
            .stat-card.medium { border-left-color: #f39c12; }
            .stat-card.weak { border-left-color: #95a5a6; }

            .stat-number {
                font-size: 3em;
                font-weight: 700;
                color: #2c3e50;
                margin-bottom: 10px;
            }

            .stat-label {
                font-size: 1.1em;
                color: #7f8c8d;
                font-weight: 500;
            }

            .methodology-section, .patents-section {
                padding: 40px;
            }

            .methodology-section h3, .patents-section h3 {
                font-size: 1.8em;
                color: #2c3e50;
                margin-bottom: 30px;
                text-align: center;
                position: relative;
            }

            .methodology-section h3::after, .patents-section h3::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 2px;
            }

            .method-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
                margin-top: 30px;
            }

            .method-card {
                background: white;
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                border-top: 4px solid;
            }

            .method-card.strong-method { border-top-color: #e74c3c; }
            .method-card.medium-method { border-top-color: #f39c12; }
            .method-card.weak-method { border-top-color: #95a5a6; }

            .method-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }

            .strength-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 10px;
            }

            .strength-indicator.strong { background: #e74c3c; }
            .strength-indicator.medium { background: #f39c12; }
            .strength-indicator.weak { background: #95a5a6; }

            .method-title {
                font-size: 1.2em;
                font-weight: 600;
                color: #2c3e50;
            }

            .method-desc {
                color: #7f8c8d;
                line-height: 1.6;
                font-size: 0.95em;
            }

            .patents-list {
                display: grid;
                gap: 20px;
            }

            .patent-card {
                background: white;
                border-radius: 15px;
                padding: 25px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                border-left: 5px solid;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .patent-card:hover {
                transform: translateX(5px);
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            }

            .patent-card.strong { border-left-color: #e74c3c; }
            .patent-card.medium { border-left-color: #f39c12; }
            .patent-card.weak { border-left-color: #95a5a6; }

            .patent-header {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-bottom: 15px;
            }

            .patent-number {
                background: #3498db;
                color: white;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 0.9em;
            }

            .strength-badge, .score-badge {
                padding: 5px 12px;
                border-radius: 20px;
                font-size: 0.8em;
                font-weight: 600;
            }

            .strength-badge.strong {
                background: rgba(231, 76, 60, 0.1);
                color: #e74c3c;
                border: 1px solid rgba(231, 76, 60, 0.3);
            }

            .strength-badge.medium {
                background: rgba(243, 156, 18, 0.1);
                color: #f39c12;
                border: 1px solid rgba(243, 156, 18, 0.3);
            }

            .strength-badge.weak {
                background: rgba(149, 165, 166, 0.1);
                color: #95a5a6;
                border: 1px solid rgba(149, 165, 166, 0.3);
            }

            .score-badge {
                background: rgba(52, 152, 219, 0.1);
                color: #3498db;
                border: 1px solid rgba(52, 152, 219, 0.3);
            }

            .patent-title {
                font-size: 1.2em;
                color: #2c3e50;
                margin-bottom: 15px;
                font-weight: 600;
                line-height: 1.4;
            }

            .patent-info {
                display: grid;
                gap: 10px;
            }

            .info-item {
                color: #7f8c8d;
                font-size: 0.95em;
                line-height: 1.5;
            }

            .info-item strong {
                color: #2c3e50;
                font-weight: 600;
            }

            .footer-section {
                background: #ecf0f1;
                padding: 30px 40px;
                text-align: center;
                border-top: 1px solid #bdc3c7;
            }

            .generation-info p {
                color: #7f8c8d;
                margin: 5px 0;
                font-size: 0.9em;
            }

            .sankey-container {
                background: white;
                margin: 20px;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            }

            @media (max-width: 768px) {
                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                }

                .method-grid {
                    grid-template-columns: 1fr;
                }

                .main-title {
                    font-size: 2em;
                }

                .analysis-container {
                    margin: 10px;
                    border-radius: 15px;
                }
            }
        </style>
        """

    def save_enhanced_html(self, output_file='enhanced_sankey_C1.html'):
        """保存增强版HTML文件"""
        fig = self.create_enhanced_sankey()
        report_html = self.generate_enhanced_report()
        css_styles = self.get_enhanced_css()

        # 获取桑基图的HTML
        sankey_html = fig.to_html(include_plotlyjs=True, div_id="sankey-plot")

        # 创建完整的HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专利技术支撑关系分析 - 多模态大语言模型协同架构</title>
    {css_styles}
</head>
<body>
    {report_html}

    <div class="sankey-container">
        <h3 style="text-align: center; color: #2c3e50; margin-bottom: 20px; font-size: 1.5em;">
            专利支撑关系桑基图
        </h3>
        <div id="sankey-plot">
            {sankey_html}
        </div>
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px; font-size: 0.9em; color: #7f8c8d;">
            <p><strong>图表说明：</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>左侧节点：相关专利技术</li>
                <li>右侧节点：科学问题</li>
                <li>连接线粗细：反映关联强度（强相关=3，中等相关=2，弱相关=1）</li>
                <li>连接线颜色：红色=强相关，蓝色=中等相关，黄色=弱相关</li>
                <li>鼠标悬停可查看详细信息</li>
            </ul>
        </div>
    </div>
</body>
</html>"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"增强版桑基图HTML文件已保存: {output_file}")
        return output_file

def main():
    """主函数"""
    generator = EnhancedSankeyGenerator()

    logger.info("开始生成增强版桑基图...")

    # 生成增强版HTML文件
    output_file = generator.save_enhanced_html()

    print(f"\n=== 增强版桑基图生成完成 ===")
    print(f"输出文件: {output_file}")
    print(f"科学问题: {generator.scientific_problem['title']}")
    print(f"相关专利数量: {len(generator.scientific_problem['patents'])}")

    # 统计信息
    patents = generator.scientific_problem['patents']
    strong_count = sum(1 for p in patents if p['strength'] == '强相关')
    medium_count = sum(1 for p in patents if p['strength'] == '中等相关')
    weak_count = sum(1 for p in patents if p['strength'] == '弱相关')

    print(f"强相关专利: {strong_count} 项")
    print(f"中等相关专利: {medium_count} 项")
    print(f"弱相关专利: {weak_count} 项")

if __name__ == "__main__":
    main()
