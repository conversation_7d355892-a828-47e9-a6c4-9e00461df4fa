#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利与科学问题关联分析脚本
分析专利与3个科学问题的支撑关系，评估关联强度
"""

import json
import xml.etree.ElementTree as ET
import re
from collections import defaultdict
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatentAnalyzer:
    def __init__(self):
        self.patents = []
        self.scientific_problems = {
            'problem1': {
                'name': '面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究',
                'keywords': [
                    '物联网', 'IoT', '大语言模型', 'LLM', '多模态', '协同', '架构', '动态优化',
                    '机器学习', '深度学习', '人工智能', 'AI', '自然语言处理', 'NLP',
                    '数据融合', '智能分析', '模型优化', '算法优化', '神经网络'
                ]
            },
            'problem2': {
                'name': '面向油气生产物联网的时序数据轻量化对象建模与源头质量协同控制方法',
                'keywords': [
                    '时序数据', '轻量化', '对象建模', '质量控制', '数据质量', '源头控制',
                    '数据采集', '数据处理', '数据存储', '数据传输', '边缘计算',
                    '实时数据', '数据压缩', '数据优化', '质控', '监测', '传感器'
                ]
            },
            'problem3': {
                'name': '油气生产物联网多智能体自组态工作流建模方法',
                'keywords': [
                    '多智能体', '自组态', '工作流', '建模', '流程编排', '业务流',
                    '智能调度', '自动化', '协作', '分布式', '任务调度', '流程优化',
                    '工作流引擎', '业务建模', '流程管理', '自适应', '动态配置'
                ]
            }
        }
        self.analysis_results = defaultdict(list)
    
    def load_patents_from_xml(self, xml_file='patents_info.xml'):
        """从XML文件加载专利信息"""
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            for patent_elem in root.findall('patent'):
                patent = {
                    'id': patent_elem.get('id'),
                    'title': patent_elem.find('title').text if patent_elem.find('title') is not None else '',
                    'abstract': patent_elem.find('abstract').text if patent_elem.find('abstract') is not None else '',
                    'inventors': []
                }
                
                # 提取发明人信息
                inventors_elem = patent_elem.find('inventors')
                if inventors_elem is not None:
                    for inventor_elem in inventors_elem.findall('inventor'):
                        patent['inventors'].append(inventor_elem.text)
                
                self.patents.append(patent)
            
            logger.info(f"成功加载 {len(self.patents)} 项专利信息")
            return True
            
        except Exception as e:
            logger.error(f"加载XML文件时出错: {e}")
            return False
    
    def calculate_relevance_score(self, patent, problem_keywords):
        """计算专利与科学问题的关联度分数"""
        text = (patent['title'] + ' ' + patent['abstract']).lower()
        
        # 关键词匹配得分
        keyword_score = 0
        matched_keywords = []
        
        for keyword in problem_keywords:
            keyword_lower = keyword.lower()
            # 精确匹配
            if keyword_lower in text:
                keyword_score += 2
                matched_keywords.append(keyword)
            # 模糊匹配（包含关键词的部分）
            elif any(part in text for part in keyword_lower.split() if len(part) > 2):
                keyword_score += 1
                matched_keywords.append(keyword + '(部分匹配)')
        
        # 标题权重更高
        title_text = patent['title'].lower()
        title_bonus = 0
        for keyword in problem_keywords:
            if keyword.lower() in title_text:
                title_bonus += 1
        
        total_score = keyword_score + title_bonus * 0.5
        
        return {
            'score': total_score,
            'matched_keywords': matched_keywords,
            'keyword_count': len(matched_keywords)
        }
    
    def classify_relevance_strength(self, score, keyword_count):
        """根据分数和关键词数量分类关联强度"""
        if score >= 5 and keyword_count >= 3:
            return '强相关'
        elif score >= 2 and keyword_count >= 2:
            return '中等相关'
        elif score >= 1 or keyword_count >= 1:
            return '弱相关'
        else:
            return '无关联'
    
    def analyze_patent_relevance(self):
        """分析专利与科学问题的关联性"""
        logger.info("开始分析专利与科学问题的关联性...")
        
        for patent in self.patents:
            patent_id = patent['id']
            patent_title = patent['title']
            
            for problem_id, problem_info in self.scientific_problems.items():
                problem_name = problem_info['name']
                keywords = problem_info['keywords']
                
                # 计算关联度分数
                relevance = self.calculate_relevance_score(patent, keywords)
                
                # 分类关联强度
                strength = self.classify_relevance_strength(
                    relevance['score'], 
                    relevance['keyword_count']
                )
                
                # 只保存有关联的专利
                if strength != '无关联':
                    self.analysis_results[problem_id].append({
                        'patent_id': patent_id,
                        'patent_title': patent_title,
                        'relevance_score': relevance['score'],
                        'strength': strength,
                        'matched_keywords': relevance['matched_keywords'],
                        'keyword_count': relevance['keyword_count']
                    })
        
        # 按关联度分数排序
        for problem_id in self.analysis_results:
            self.analysis_results[problem_id].sort(
                key=lambda x: x['relevance_score'], 
                reverse=True
            )
        
        logger.info("专利关联性分析完成")
        return self.analysis_results
    
    def save_analysis_results(self, output_file='patent_analysis_results.json'):
        """保存分析结果"""
        results = {
            'scientific_problems': self.scientific_problems,
            'analysis_results': dict(self.analysis_results),
            'summary': {}
        }
        
        # 生成摘要统计
        for problem_id, patents in self.analysis_results.items():
            problem_name = self.scientific_problems[problem_id]['name']
            strength_counts = defaultdict(int)
            
            for patent in patents:
                strength_counts[patent['strength']] += 1
            
            results['summary'][problem_id] = {
                'problem_name': problem_name,
                'total_related_patents': len(patents),
                'strength_distribution': dict(strength_counts)
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分析结果已保存到: {output_file}")
        return output_file

def main():
    """主函数"""
    analyzer = PatentAnalyzer()
    
    # 加载专利数据
    if not analyzer.load_patents_from_xml():
        logger.error("无法加载专利数据，程序退出")
        return
    
    # 分析专利关联性
    results = analyzer.analyze_patent_relevance()
    
    # 保存分析结果
    output_file = analyzer.save_analysis_results()
    
    # 打印摘要
    print("\n=== 专利与科学问题关联分析摘要 ===")
    for problem_id, problem_info in analyzer.scientific_problems.items():
        problem_name = problem_info['name']
        related_patents = results.get(problem_id, [])
        
        print(f"\n科学问题: {problem_name}")
        print(f"相关专利数量: {len(related_patents)}")
        
        if related_patents:
            strength_counts = defaultdict(int)
            for patent in related_patents:
                strength_counts[patent['strength']] += 1
            
            for strength, count in strength_counts.items():
                print(f"  {strength}: {count} 项")
            
            print("  前5项最相关专利:")
            for i, patent in enumerate(related_patents[:5], 1):
                print(f"    {i}. {patent['patent_title']} (分数: {patent['relevance_score']:.1f}, {patent['strength']})")

if __name__ == "__main__":
    main()
