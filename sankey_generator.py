#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桑基图生成脚本
为每个科学问题创建桑基图HTML文件，展示专利→科学问题的支撑关系流向
"""

import json
import plotly.graph_objects as go
from plotly.offline import plot
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SankeyGenerator:
    def __init__(self):
        self.analysis_results = {}
        self.scientific_problems = {}
        
        # 关联强度对应的颜色和权重
        self.strength_config = {
            '强相关': {'color': '#FF6B6B', 'weight': 3, 'opacity': 0.8},
            '中等相关': {'color': '#4ECDC4', 'weight': 2, 'opacity': 0.6},
            '弱相关': {'color': '#45B7D1', 'weight': 1, 'opacity': 0.4}
        }
    
    def load_analysis_results(self, file_path='patent_analysis_results.json'):
        """加载专利分析结果"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.analysis_results = data['analysis_results']
                self.scientific_problems = data['scientific_problems']
            
            logger.info(f"成功加载分析结果，包含 {len(self.analysis_results)} 个科学问题")
            return True
            
        except Exception as e:
            logger.error(f"加载分析结果时出错: {e}")
            return False
    
    def create_sankey_for_problem(self, problem_id):
        """为特定科学问题创建桑基图"""
        if problem_id not in self.analysis_results:
            logger.warning(f"未找到科学问题 {problem_id} 的分析结果")
            return None
        
        problem_name = self.scientific_problems[problem_id]['name']
        related_patents = self.analysis_results[problem_id]
        
        if not related_patents:
            logger.warning(f"科学问题 {problem_id} 没有相关专利")
            return None
        
        # 准备桑基图数据
        nodes = []
        links = []
        
        # 添加专利节点（源节点）
        patent_nodes = {}
        for i, patent in enumerate(related_patents):
            patent_title = patent['patent_title']
            # 截断过长的标题
            if len(patent_title) > 30:
                display_title = patent_title[:27] + "..."
            else:
                display_title = patent_title
            
            nodes.append(display_title)
            patent_nodes[patent['patent_id']] = i
        
        # 添加科学问题节点（目标节点）
        problem_node_index = len(nodes)
        # 截断过长的问题名称
        if len(problem_name) > 40:
            display_problem_name = problem_name[:37] + "..."
        else:
            display_problem_name = problem_name
        nodes.append(display_problem_name)
        
        # 创建连接
        for patent in related_patents:
            source_index = patent_nodes[patent['patent_id']]
            target_index = problem_node_index
            strength = patent['strength']
            weight = self.strength_config[strength]['weight']
            
            links.append({
                'source': source_index,
                'target': target_index,
                'value': weight,
                'color': self.strength_config[strength]['color'],
                'opacity': self.strength_config[strength]['opacity'],
                'strength': strength,
                'score': patent['relevance_score']
            })
        
        # 创建桑基图
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                label=nodes,
                color="lightblue"
            ),
            link=dict(
                source=[link['source'] for link in links],
                target=[link['target'] for link in links],
                value=[link['value'] for link in links],
                color=[link['color'] for link in links],
                hovertemplate='<b>%{source.label}</b> → <b>%{target.label}</b><br>' +
                             '关联强度: %{customdata[0]}<br>' +
                             '关联分数: %{customdata[1]:.1f}<extra></extra>',
                customdata=[[link['strength'], link['score']] for link in links]
            )
        )])
        
        # 设置图表标题和布局
        fig.update_layout(
            title={
                'text': f"专利支撑关系桑基图<br><sub>{problem_name}</sub>",
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            font_size=10,
            width=1200,
            height=800,
            margin=dict(l=50, r=50, t=100, b=50)
        )
        
        return fig
    
    def generate_analysis_report(self, problem_id):
        """生成分析报告"""
        if problem_id not in self.analysis_results:
            return ""
        
        problem_name = self.scientific_problems[problem_id]['name']
        related_patents = self.analysis_results[problem_id]
        
        # 统计各强度级别的专利数量
        strength_counts = {}
        for patent in related_patents:
            strength = patent['strength']
            strength_counts[strength] = strength_counts.get(strength, 0) + 1
        
        # 生成报告
        report = f"""
        <div style="margin: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
            <h2>关联度评估分析报告</h2>
            <h3>科学问题：{problem_name}</h3>
            
            <h4>总体统计</h4>
            <ul>
                <li>相关专利总数：{len(related_patents)} 项</li>
                <li>强相关专利：{strength_counts.get('强相关', 0)} 项</li>
                <li>中等相关专利：{strength_counts.get('中等相关', 0)} 项</li>
                <li>弱相关专利：{strength_counts.get('弱相关', 0)} 项</li>
            </ul>
            
            <h4>关联度评估依据</h4>
            <p><strong>评估方法：</strong>基于关键词匹配和语义分析，计算专利文本与科学问题的相关性分数。</p>
            <ul>
                <li><strong>强相关（红色）：</strong>关联分数 ≥ 5 且匹配关键词 ≥ 3 个</li>
                <li><strong>中等相关（青色）：</strong>关联分数 ≥ 2 且匹配关键词 ≥ 2 个</li>
                <li><strong>弱相关（蓝色）：</strong>关联分数 ≥ 1 或匹配关键词 ≥ 1 个</li>
            </ul>
            
            <h4>前10项最相关专利</h4>
            <ol>
        """
        
        # 添加前10项专利
        for i, patent in enumerate(related_patents[:10], 1):
            report += f"""
                <li>
                    <strong>{patent['patent_title']}</strong><br>
                    关联强度：{patent['strength']} | 关联分数：{patent['relevance_score']:.1f} | 
                    匹配关键词数：{patent['keyword_count']}
                </li>
            """
        
        report += """
            </ol>
            
            <h4>桑基图说明</h4>
            <p>桑基图展示了专利与科学问题之间的支撑关系流向：</p>
            <ul>
                <li>左侧节点：相关专利</li>
                <li>右侧节点：科学问题</li>
                <li>连接线粗细：反映关联强度（强相关=3，中等相关=2，弱相关=1）</li>
                <li>连接线颜色：红色=强相关，青色=中等相关，蓝色=弱相关</li>
            </ul>
            
            <p><em>生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</em></p>
        </div>
        """
        
        return report
    
    def save_sankey_html(self, problem_id, fig, output_dir='.'):
        """保存桑基图为HTML文件"""
        problem_name_short = self.scientific_problems[problem_id]['name'][:20]
        filename = f"{output_dir}/桑基图_{problem_id}_{problem_name_short}.html"
        
        # 生成分析报告
        report = self.generate_analysis_report(problem_id)
        
        # 创建完整的HTML内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>专利支撑关系桑基图 - {problem_name_short}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .container {{ max-width: 1400px; margin: 0 auto; }}
            </style>
        </head>
        <body>
            <div class="container">
                {report}
                <div id="sankey-plot">
                    {fig.to_html(include_plotlyjs=True, div_id="sankey-plot")}
                </div>
            </div>
        </body>
        </html>
        """
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"桑基图HTML文件已保存: {filename}")
        return filename

def main():
    """主函数"""
    generator = SankeyGenerator()
    
    # 加载分析结果
    if not generator.load_analysis_results():
        logger.error("无法加载分析结果，程序退出")
        return
    
    # 为每个科学问题生成桑基图
    generated_files = []
    for problem_id in generator.analysis_results.keys():
        logger.info(f"正在为科学问题 {problem_id} 生成桑基图...")
        
        fig = generator.create_sankey_for_problem(problem_id)
        if fig:
            filename = generator.save_sankey_html(problem_id, fig)
            generated_files.append(filename)
        else:
            logger.warning(f"无法为科学问题 {problem_id} 生成桑基图")
    
    print(f"\n=== 桑基图生成完成 ===")
    print(f"共生成 {len(generated_files)} 个HTML文件：")
    for filename in generated_files:
        print(f"  - {filename}")

if __name__ == "__main__":
    main()
