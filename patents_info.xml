<?xml version="1.0" ?>
<patents total_count="99" generated_date="2025-08-27 11:30:37">
  <patent id="1">
    <title>一种针对VSP处理解释数据的综合质控方法及相关设备</title>
    <inventors>
      <inventor>中国石油天然气股份有限公司</inventor>
    </inventors>
    <abstract>本发明公开一种针对VSP处理解释数据的综合质控方法及相关设备,分别对VSP数据预处理与初至拾取、信号处理、走廊叠加与成像、VSP综合解释四大工序进行质量控制,实现了VSP数据远程质控；将质控工作由线下模式转到线上；量化质控；质控报告可以一键生成,提高了质控系统的灵活性以及工作效率,降低了质控成本,从统一质控标准、远程在线质控的目标出发,研发了波峰与初至误差、反褶积效果分析、同相轴追踪算法、动校正Gamma值、速度误差分析、DB谱的计算公式、Tar值的计算方法等VSP关键算法,为塔里木油田VSP处理解释项目四道工序提供了统一的质控标准,确保了数据质控结果的科学性及可靠性。</abstract>
  </patent>
  <patent id="2">
    <title>一种油气物联网的管理系统、管理方法、电子设备及介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请提供了一种油气物联网的管理系统、管理方法、电子设备及介质,油气物联网包括多个传感器节点和多个中继节点；管理系统包括：节点管理模块、数据采集模块、第一数据处理模块、第二数据处理模块、路径规划模块和数据传输模块；通过油气物联网中环境数据的传输权重进行数据融合,能够减少网络中无用数据和冗余数据的传输,减少网络能量的无用消耗,节约网络能源,延长网络寿命；此外,根据传输权重确定数据传输顺序并规划数据传输路径,能够提高数据传输的及时性和传输效率。</abstract>
  </patent>
  <patent id="3">
    <title>一种用于油气行业的工作流程编排方法及业务流建模方法</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明涉及一种用于油气行业的工作流程编排方法及业务流建模方法,该工作流程编排方法包括：创建流程模板,为所述流程模板的编排界面配置多个功能节点；响应于编排指令,针对业务流模型中的每一业务流节点,根据其工作场景获取对应的至少一个功能节点在画布上进行编排,并通过不同的连线方式标识节点之间的数据传递方式,完成工作流程编排,得到流程模型；接收当前研究对象的实体数据,基于所述流程模型对所述实体数据进行处理,得到业务结果。本发明能够实现线上跨专业、跨部门之间的数据成果流转与协作,支撑科研、生产与决策的高效互动,解决了工作流引擎不满足实际业务场景的问题。</abstract>
  </patent>
  <patent id="4">
    <title>一种油田等值线图的生成方法、装置及电子设备</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本公开提供了一种油田等值线图的生成方法、装置及电子设备,通过获取基础油田数据,根据所述基础油田数据中包括的油井属性数据,绘制基础油田平面图；确定所述基础油田数据中包括的待绘图数据,采用反距离加权差值算法针对所述待绘图数据进行插值构建目标值网格,并根据所述目标值网格生成基础等值线图；提供配置有预设等值线各向异性参数的画刷工具,响应于用户针对所述画刷工具在所述基础等值线图中的移动操作,根据所述预设等值线各向异性参数,更新所述画刷工具所处位置对应的所述目标值网格处的网格值；根据更新后的所述目标值网格生成目标等值线图。可以将多个参数源绘制到一个等高线范围内,提升等值线图生成效果。</abstract>
  </patent>
  <patent id="5">
    <title>一种油田电网关键节点识别方法及装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种油田电网关键节点识别方法及装置,该方法包括：根据获取的油田电网节点拓扑图,得到油田电网加权图；计算所述油田电网加权图中每个节点的度、介数和功率聚集系数；根据所述油田电网加权图,计算得到每个节点的结构洞约束系数；根据所述每个节点的度、介数和功率聚集系数,基于改进的K-Shell算法计算得到每个节点的核值；根据所述每个节点的核值和结构洞约束系数,计算得到每个节点的Tsallis熵；根据每个节点的Tsallis熵,计算得到每个节点的关键性评估值；根据所述每个节点的关键性评估值与预设比例,得到油田电网的关键节点。该方法在电网中能够高效、准确地识别关键节点,相较于传统方法有显著的提高识别率精度。</abstract>
  </patent>
  <patent id="6">
    <title>基于边缘计算的抽油机井能耗优化控制方法、装置及系统</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了一种基于边缘计算的抽油机井能耗优化控制方法、装置及系统。该方法实时获取抽油机井系统的系统环境状态,通过基于DDPG的抽油机井能耗优化控制模型,根据当前的系统环境状态,基于随机策略,得到当前控制策略,并发送给抽油机井系统；获取抽油机井系统执行当前控制策略后,得到的下一时刻的系统环境状态以及即时回报,并与当前的系统环境状态和当前控制策略整合为特定状态转移数据；重复上述得到状态转移数据的过程,直至获取到设定数量的状态转移数据；然后随机抽取多个状态转移数据,更新抽油机井能耗优化控制模型的参数；重复上述获取状态转移数据并进行参数更新的过程,直至满足预设优化条件,得到最优抽油机井能耗优化控制策略。</abstract>
  </patent>
  <patent id="7">
    <title>一种基于声纹识别的阀室泄漏检测方法、装置及系统</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了一种基于声纹识别的阀室泄漏检测方法、装置及系统,该方法包括将获得的阀室各拾音器的音频输入预设浅层特征提取算法中,得到对应的音频的浅层特征；将音频的浅层特征输入预训练后的深层特征提取模型中,得到对应的音频的深层特征；将音频的深层特征输入第一分类模型,将音频的浅层特征输入第二分类模型和第三分类模型,分别进行泄漏检测,得到对应的第一检测结果、第二检测结果和第三检测结果；根据预先确定的第一分类模型、第二分类模型和第三分类模型的预测权重,以及第一检测结果、第二检测结果和第三检测结果,基于软投票方式,确定音频的泄漏检测结果。该方法基于声纹识别,通过训练并部署声纹分类模型进行泄漏检测,准确率高。</abstract>
  </patent>
  <patent id="8">
    <title>油井产量的计量方法、装置、设备及存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了油井产量的计量方法、装置、设备及存储介质,属于油气开发生产技术领域。在本申请中,搭建了机理模型和数据模型混合油井计量模型,获取油井的实时生产数据后,根据模型选取条件从油井计量模型中选取目标模型,利用选取的目标模型对实时生产数据进行处理,得到油井产量。由于可以根据模型选取条件选择进行产量计量的目标模型,解决了单一模型无法满足计算条件,进而无法进行产量预测的问题,扩大了油井产量的计量模型的应用范围。另一方面,该机理模型通过机理校正参数对初始机理模型进行校正之后得到,机理校正参数可以由数据模型对历史数据进行学习后得到,解决了机理模型校正难度大的问题。</abstract>
  </patent>
  <patent id="9">
    <title>一种基于数字孪生数模分离的可视化智能巡检方法和装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种基于数字孪生数模分离的可视化智能巡检方法和装置。所述方法包括,利用数字孪生和数模分离技术建立油气田场站三维可视化模型,构建道路网络,基于道路网络定制巡检路线；在虚拟巡检员按巡检路线巡检过程中,按设定间隔更新模型中待巡检设备的当前数据序列,通过时间序列预测模型进行故障预测；若预测到任一待巡检设备存在故障风险,根据道路网络,确定从虚拟巡检员的当前位置到存在故障风险的待巡检设备的最短路径,使得虚拟巡检员按最短路径到达该设备,进行相应的处理。该方法能够基于数字孪生数模分离实现油气田场站工作场景的轻量级仿真模型建立,并通过运行数据的挖掘分析进行故障的预测,实现巡检质量与效率的快速提升。</abstract>
  </patent>
  <patent id="10">
    <title>用电异常用户的确定方法、装置、设备</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了一种用电异常用户的确定方法、装置、设备,涉及油田电力领域,该方法包括如下步骤。采集电气数据,电气数据包括与多个用户分别对应的电压数据和功率数据；基于电压数据对多个用户进行聚类分析,得到多个聚类簇,每个聚类簇对应有聚类簇电压数据和聚类簇功率数据；基于聚类簇电压数据和聚类簇功率数据确定多个聚类簇分别对应的斯皮尔曼相关系数；响应于多个聚类簇中的目标聚类簇对应的斯皮尔曼相关系数符合预设的异常要求,基于目标聚类簇中多个用户分别对应的电压数据和功率数据,从目标聚类簇中确定用电异常用户。能够识别出油田电网中用电异常的用户,避免受环境干扰的影响,提高用电异常用户的识别结果的准确性。</abstract>
  </patent>
  <patent id="11">
    <title>油田电网中关键节点的识别方法、装置及存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了一种油田电网中关键节点的识别方法、装置及存储介质,属于油气开发领域。所述方法包括：构建油田电网的有向拓扑图；基于有向拓扑图中具有连接关系的两个节点之间的电气特性,确定用于连接两个节点的边的权重,得到有向拓扑图中每条边的权重；基于有向拓扑图和有向拓扑图中每条边的权重,确定多个节点分别对应的核值；将目标核值范围划分为多个核值区间；按照多个核值区间,对多个节点进行筛选,以确定出核值位于多个核值区间中最大核值区间内的节点,将位于最大核值区间内的节点作为油田电网中的关键节点。本申请采用结合电气特性且多样化的指标来确定节点核值,进而对节点进行筛选,能够准确地识别出油田电网中的关键节点集。</abstract>
  </patent>
  <patent id="12">
    <title>存储状态数据的方法、装置、设备和存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
  </patent>
  <patent id="13">
    <title>Summary-摘要: 本公开实施例提供了一种存储状态数据的方法、装置、设备和存储介质,属于油气勘探开发数据处理技术领域。该方法中,使用数据存储系统对状态数据进行存储,数据存储系统中包括多个边缘存储设备和中心存储设备,这样由多个存储设备分担相应的处理压力,减少单个数据存储设备的处理压力,对于短时大量产生的状态数据可以及时处理,防止状态数据不能快速及时存储,防止状态数据丢失。</title>
  </patent>
  <patent id="14">
    <title>一种井控面积的确定方法、确定装置、设备及介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请提供了一种井控面积的确定方法、确定装置、设备及介质,所述方法包括：获取目标区域对应的多井渗透率等值线图；基于每个单井在所述多井渗透率等值线图中的位置坐标以及所述多井渗透率等值线图中所携带的渗透率等值线确定每个单井的渗透率值；在所述多井渗透率等值线图中利用每个单井的渗透率值同时对多个单井进行所述含油气面积边界内的流体传导,确定出每个单井对应的单井控制面积；将多个单井对应的单井控制面积进行求和,以确定出所述目标区域内的总井控面积。通过所述方法和装置,更合理、更快速且更加精准的确定井控面积,提高了确定出的井控面积的准确性。</abstract>
  </patent>
  <patent id="15">
    <title>基于混合策略的智能事件触发的风险检测方法及装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种基于混合策略的智能事件触发的风险检测方法及装置。该方法包括按照预设的时间间隔截帧图像；获取当前截帧图像对应的预先构建的目标检测模型,并判断目标检测模型的风险等级是否低于预设风险等级；若是,将当前截帧图像与预先构建的图片储存库中前一时刻截帧图像进行相似度计算,得到汉明距离；判断汉明距离是否小于预先确定的相似度阈值；若是,获取前一时刻截帧图像对应的历史推理结果,将历史推理结果作为当前截帧图像的推理结果；若否,使用目标检测模型对当前截帧图像进行目标检测,得到推理结果。该方法解决了目前基于时间周期的事件触发造成的计算资源浪费,以及存储空间占用过大的问题,平衡了检测性能和检测成本。</abstract>
  </patent>
  <patent id="16">
    <title>油气储量计算单元跨带含油气区域面积计算方法及装置</title>
    <inventors>
      <inventor>三门峡市宏基机械有限公司</inventor>
    </inventors>
    <abstract>本实用新型公开了一种用于大型罐体和塔体安装的平衡吊装梁,包括主支撑梁、位于主支撑梁两侧的侧加强筋板以及位于主支撑梁上方的顶部加强筋板；所述顶部加强筋板上设置有两个吊耳孔,配置为用于将吊装梁吊起；所述主支撑梁的两端各设置有一个钢丝绳防割断空心钢管,钢丝绳防割断空心钢管的外端分别各设置有一个钢丝绳固定卡扣。本实用新型的吊装梁能够撑开吊装钢筋绳形成一定的夹角,在设备起立过程中,防止吊装钢筋绳被设备表面的接管磕绊,避免设备损坏和安全事故,也减少了设备表面与吊装钢筋绳摩擦产生的划痕。</abstract>
  </patent>
  <patent id="17">
    <title>一种基于等值线图的计算等值线间面积方法、装置及设备</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种基于等值线图的计算等值线间面积方法、装置及设备,该方法包括对预先获得的含油气面积约束的等值线图进行闭合处理,得到闭合的等值线图；根据闭合的等值线图中等值线与边界线交点的参数值,确定边界线的参数值；对闭合的等值线图中所有点信息进行三角拓扑识别,得到所有初始三角面片元；对每一初始三角面片元中的符合预设规则的初始三角面片元进行细化,得到所有处理后三角面片元；根据所有处理后三角面片元,确定闭合的等值线图的所有边界线和等值线的位置；根据等值线和边界线的参数值和位置,确定所有等值线间面积。该方法可以在不用人为干预的条件下自动化实现等值线间面积拾取,可以保证数据精度并获得极高的工作效率。</abstract>
  </patent>
  <patent id="18">
    <title>一种知识融合的方法和装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种知识融合的方法和装置。所述方法包括：根据第一本体的第一本体概念的描述信息和第一本体关系的路径信息、第二本体的第二本体概念的描述信息和第二本体关系的路径信息,确定两个本体的概念相似度和关系相似度；根据概念相似度和关系相似度对齐两个本体,生成包括第三本体概念的第三本体；从第一本体对应的第一实体集合中和第二本体对应的第二实体集合中,获取指向同一第三本体概念的实体,得到第三实体集合；确定第三实体集合中每两个实体的实体综合相似度,对齐实体综合相似度符合预设的实体相似条件的两个实体。能够在少量标注数据的情况下进行知识融合,并更准确地融合文本差异较大的相匹配元素,提高知识融合的准确率。</abstract>
  </patent>
  <patent id="19">
    <title>一种图表数据的提取方法和装置</title>
    <inventors>
      <inventor>中国石油大学(北京)</inventor>
    </inventors>
    <abstract>本发明涉及一种适用于智能物联网的微功耗嵌入式传感器休眠电路,包括微控制器、物理传感器、通讯模组、磁感模组和电源电路；物理传感器,用于检测物理量,并将检测的物理量转换为数字信号；磁感模组,用于检测磁场变化,并将磁场变化转换为数字信号；电源电路,用于为各用电器件进行供电；通讯模组,用于实现电子设备之间的数据通信；微控制器通过通讯模组连接物理传感器和磁感模组,所述微控制器,用于通过所述通讯模组连接所述物理传感器和磁感模组,并通过动态控制算法控制所述磁感模组、通讯模组和物理传感器的工作。因此,本发明在智能物联网系统的终端侧应用中,休眠电路有效地降低系统的用户设备能耗,延长电池供电类系统的运行时间。</abstract>
  </patent>
  <patent id="20">
    <title>一种面向油气生产物联网控制系统的自组态建模方法</title>
    <inventors>
      <inventor>中国石油大学(北京)</inventor>
    </inventors>
    <abstract>本发明涉及一种面向油气生产物联网控制系统的自组态建模方法,该方法包括：对油气生产现场所监管的各类设备对象和/或生产单元进行抽象,构建相应的油气物模型；通过云组态建模软件搭建可视化组态场景应用,关联绑定相匹配的油气物模型；在油气生产现场部署传感器和控制器,实现以油气物模型为主体的批量实例化对象的设备数据采集、远程传输、实时监测和智能操控的数据联动。本发明能够大幅降低专业人员搭建组态场景应用的工作量和时间成本,缩短开发周期,提升油气生产物联网控制系统的运维和交付效率,助力油气田企业对生产过程现场实时监测和闭环管控。</abstract>
  </patent>
  <patent id="21">
    <title>ESummary-主权项: 1.一种面向油气生产物联网控制系统的自组态建模方法,其特征在于,该方法包括：对油气生产现场所监管的各类设备对象和/或生产单元进行抽象,构建相应的油气物模型；通过云组态建模软件搭建可视化组态场景应用,关联绑定相匹配的油气物模型；在油气生产现场部署传感器和控制器,实现以油气物模型为主体的批量实例化对象的设备数据采集、远程传输、实时监测和智能操控的数据联动。</title>
  </patent>
  <patent id="22">
    <title>问答搜索引擎的构建方法和查询业务数据的方法</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请公开了一种问答搜索引擎的构建方法和查询业务数据的方法,属于互联网技术领域。方法包括：创建问答搜索引擎；关联问答搜索引擎和业务数据库对应的知识库元数据；为问答搜索引擎配置多个意图信息；为意图信息分别配置对应的答案信息和多个问题信息；标注多个问题信息的关键信息,关键信息用于基于接收到的第一问题信息,从多个问题信息中确定与第一问题信息匹配的第二问题信息,确定第二问题信息对应的目标意图信息,确定目标意图信息对应的答案信息。本申请可以提高查询业务数据的效率。</abstract>
  </patent>
  <patent id="23">
    <title>一种油气生产智能物联网数据自清洗与修正补齐方法</title>
    <inventors>
      <inventor>中国石油大学(北京)</inventor>
    </inventors>
    <abstract>本发明涉及一种油气生产智能物联网数据自清洗与修正补齐方法,包括：采集原始数据,将原始数据转换为数据队列,其中,原始数据为面向石油能源化工领域油气生产智能物联网系统的时序数据；将数据队列进行预处理；将预处理后的数据队列进行异常检测获得异常时序数据；对异常时序数据进行剥离并压缩存储,获得正常时序数据队列和异常时序数据队列；对异常时序数据队列进行数据清洗及补齐修正,将补齐修正后的时序数据与正常时序数据队列进行合并完成数据队列补充。本发明对解决了石油化工能源行业物联网数据采集过程中存在的噪声污染、测量值缺失和错误数据等数据质量不高的问题,可以有效提高油气生产数据的质量和可靠性。</abstract>
  </patent>
  <patent id="24">
    <title>基于物联网设备的通信方法、装置、设备和存储介质</title>
    <inventors>
      <inventor>东杰智能科技集团股份有限公司</inventor>
    </inventors>
    <abstract>本实用新型公开了一种直行轨道与弧形轨道的转换道岔机构,解决了如何设计一种简单、便捷操作的转换道岔的问题；包括轨道盘(1),在轨道盘(1)的中心处固定设置有中心轴承(4),在轨道盘(1)上分别设置有外圈环形轨道(2)和内圈环形轨道(3),在回转盘(5)的下底面上,分别等间隔弧度地设置有外圈支撑滚轮机构(12)和内圈支撑滚轮机构(13),在回转盘的顶端面上,分别设置有回转盘上直行轨道(6)和回转盘上弧形轨道(7)；在回转盘的外圆上设置有弧形齿条段(8),在旋转驱动电机(10)的输出轴上设置有驱动齿轮(11),驱动齿轮与弧形齿条段啮合在一起；通过回转盘的旋转可快速实现直行轨道与弧形轨道的切换。</abstract>
  </patent>
  <patent id="25">
    <title>City-地址: 030000 山西省太原市中北高新技术产业开发区丰源路59号</title>
  </patent>
  <patent id="26">
    <title>一种用于智能问答的文本解析方法和装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种用于智能问答的文本解析方法和装置。该方法包括：基于正则实体检测规则,获取查询语句的正则实体识别结果；基于专业词典和同义词词典,对查询语句进行分词、词性标注及同义词识别,得到分词标注结果；基于特殊词汇词典,获取查询语句的特殊词汇识别结果；基于上述结果制作问句解析词典；根据问句解析词典和配置的问句模式识别依据,确定查询语句的问句模式；问句模式识别依据根据目标领域的典型问题配置；根据查询语句的问句模式确定解析语句,根据问句解析词典和确定的解析语句的问句意图,锁定查询数据库及数据表并生成查询表达式。不依赖大规模语料库实现自动文本解析、提供便捷快速的访问服务,允许用户配置,可扩展性强。</abstract>
  </patent>
  <patent id="27">
    <title>一种基于数字孪生数模分离的可视化智能巡检方法和装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种基于数字孪生数模分离的可视化智能巡检方法和装置。所述方法包括,利用数字孪生和数模分离技术建立油气田场站三维可视化模型,构建道路网络,基于道路网络定制巡检路线；在虚拟巡检员按巡检路线巡检过程中,按设定间隔更新模型中待巡检设备的当前数据序列,通过时间序列预测模型进行故障预测；若预测到任一待巡检设备存在故障风险,根据道路网络,确定从虚拟巡检员的当前位置到存在故障风险的待巡检设备的最短路径,使得虚拟巡检员按最短路径到达该设备,进行相应的处理。该方法能够基于数字孪生数模分离实现油气田场站工作场景的轻量级仿真模型建立,并通过运行数据的挖掘分析进行故障的预测,实现巡检质量与效率的快速提升。</abstract>
  </patent>
  <patent id="28">
    <title>一种预警方法、装置、电子设备及计算机可读存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请实施例提供的一种预警方法、装置、电子设备及计算机可读存储介质,其中,该方法包括：根据获取的包含有静电桩和目标移动对象的目标视频数据,确定目标移动对象与静电桩的最近距离信息；在最近距离信息不大于第一预设值的情况下,判断目标移动对象的目标检测部位所在的检测框与静电桩所在的预测框的交并比是否大于第二预设值；若交并比大于第二预设值,则判断目标移动对象的目标检测部位与静电桩的连续接触时间是否小于预设时间；若连续接触时间小于预设时间,则对目标移动对象进行预警。通过自动判断目标移动对象是否触摸静电桩,起到预警的作用,以提高人员的安全性及监管过程中的自动化。</abstract>
  </patent>
  <patent id="29">
    <title>一种基于等值线图的计算等值线间面积方法、装置及设备</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本发明公开了一种基于等值线图的计算等值线间面积方法、装置及设备,该方法包括对预先获得的含油气面积约束的等值线图进行闭合处理,得到闭合的等值线图；根据闭合的等值线图中等值线与边界线交点的参数值,确定边界线的参数值；对闭合的等值线图中所有点信息进行三角拓扑识别,得到所有初始三角面片元；对每一初始三角面片元中的符合预设规则的初始三角面片元进行细化,得到所有处理后三角面片元；根据所有处理后三角面片元,确定闭合的等值线图的所有边界线和等值线的位置；根据等值线和边界线的参数值和位置,确定所有等值线间面积。该方法可以在不用人为干预的条件下自动化实现等值线间面积拾取,可以保证数据精度并获得极高的工作效率。</abstract>
  </patent>
  <patent id="30">
    <title>一种碳酸氢铵碳化塔塔内结晶物清除装置</title>
    <inventors>
      <inventor>三门峡市宏基机械有限公司</inventor>
    </inventors>
    <abstract>本实用新型公开了一种碳酸氢铵碳化塔塔内结晶物清除装置,其包括碳化塔本体,所述碳化塔本体的底部固定安装有若干个支撑腿,所述碳化塔本体顶部的一侧固定穿插安装有进料管,所述碳化塔本体的底部固定穿插安装有排料管。该一种碳酸氢铵碳化塔塔内结晶物清除装置,本实用新型通过碳化塔本体、支撑腿、排料管、竖管、横向连接管、电机、固定架、第一锥形齿轮、第二锥形齿轮、进料管和刮板的配合使用,横向连接管的转动直接带动刮板沿着碳化塔本体的内壁移动,在移动的过程中,会直接将碳化塔本体内壁上结晶物清理下来,在整个清除的过程中,刮板始终紧密地贴合碳化塔本体内壁,避免清洗死角。</abstract>
  </patent>
  <patent id="31">
    <title>数据处理方法、装置、系统、电子设备及可读存储介质</title>
    <inventors>
      <inventor>中国石油天然气集团有限公司</inventor>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种数据处理方法、装置、系统、电子设备及可读存储介质,其中该方法包括：在接收到整合部署文件后提取加密目标模型文件、调用工具包和密码文件；加密目标模型文件是使用目标加密方式利用随机数和指定数据对目标模型文件加密得到的；调用工具包包括主函数接口和密码校验模块；通过主函数接口调用密码校验模块,从密码文件中获取第一编码数据,并使用预设解码方式对第一编码数据进行解码得到随机数；使用目标加密方式对随机数和密码校验动态库中预置的指定数据进行加密得到解密密钥；利用解密密钥对加密目标模型文件进行解密,以将解密后的目标模型文件部署在应用端。通过该方法有利于提高目标模型文件和调用工具包的安全性。</abstract>
  </patent>
  <patent id="32">
    <title>数据代理装置、方法、电子设备、系统及存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请提供一种数据代理装置、方法、电子设备、系统及存储介质。其中装置包括：报文接收单元,用于接收多个终端设备发送的物联网报文,对接收到的物联网报文进行去重操作；报文识别单元,用于根据预设的验证规则和业务规则,验证去重后的物联网报文是否为噪音报文；报文解析单元,用于根据报文解析模板,对验证后的非噪音报文进行解析,得到解析后的数据,其中,不同类型的终端设备配置有不同的报文解析模板；数据融合单元,用于将不同终端设备对应的解析后的数据,和/或,同一终端设备在不同时段对应的解析后的数据进行融合；数据上报单元,用于将融合后的数据发送至物联网平台进行处理,可以减轻物联网平台处理数据的压力,提升处理效率。</abstract>
  </patent>
  <patent id="33">
    <title>一种油气藏配产数据确定和能源井一体化模拟方法及装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本说明书涉及油气藏开发生产技术领域,尤其涉及一种油气藏配产数据确定和能源井一体化模拟方法及装置。其中油气藏配产数据确定方法包括利用气藏数值模拟模型对能源井采集得到的第一瞬态井数据进行处理,得到流动表现方程和饱和度数据；利用井筒模型针对表征数据和流动表现方程进行处理,得到垂直提升表现方程,表征数据由饱和度数据确定；联立流动表现方程和垂直提升表现方程,求解得到交点数据；以及基于地面管网模型针对与终端分离器对应的参数和交点数据进行处理,得到目标流压数据和目标配产量数据。利用本说明书实施例,实现了在气藏地下渗流、井筒多相管流和地面管网集输一体化的基础上,确定目标流压数据和目标配产量数据。</abstract>
  </patent>
  <patent id="34">
    <title>一种防爆视频分析设备、方法、电子设备以及存储介质</title>
    <inventors>
      <inventor>中国石油天然气集团有限公司</inventor>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本公开提供了一种防爆视频分析设备、方法、电子设备以及存储介质,该设备包括：防爆摄像头、视频分析主板、通信模块,其中,防爆摄像头用于实时采集防爆场所中目标作业区域的视频数据；视频分析主板用于利用与合规检测类型对应的目标分析模型,对视频数据进行视频分析,得到视频分析结果；通信模块用于将视频分析结果发送控制的中心系统,以便控制中心系统在确定目标作业区域存在危险事件时进行预警。这样,防爆视频分析设备可以用于防爆场所,且设备具有视频分析功能,可以分析出目标作业区域是否存在违规行为或违规人员,既实现了设备的防爆性的同时,又实现对防爆场所的实时数据分析,提升了防爆场所的安全性。</abstract>
  </patent>
  <patent id="35">
    <title>一种能源井配产数据确定方法、装置及计算机设备</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本说明书涉及油气藏开发技术领域,尤其涉及一种能源井配产数据确定方法、装置及计算机设备。其中能源井配产数据确定方法包括根据接收到的能源井配产请求,确定能源井配产请求包括的配产目标和配产约束条件；确定满足配产目标和配产约束条件的多个配产数据集合,每个配产数据集合包括多个配产数据,每个配产数据分别与能源井相对应；利用气藏井筒管网模型针对多个配产数据集合进行处理,得到第一优化指标数据；以及基于第一优化指标数据,对多个配产数据集合进行优化处理,得到与每个能源井对应的目标配产数据。利用本说明书实施例,实现了在考虑气藏、井筒和管网各个环节的基础上,针对每个能源井合理地确定目标配产数据。</abstract>
  </patent>
  <patent id="36">
    <title>一种漏油检测的方法、装置、计算机设备和介质</title>
    <inventors>
      <inventor>中国石油天然气集团有限公司</inventor>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种漏油检测的方法、装置、计算机设备和介质,所述方法包括：获取目标区域中每个监测位置的采集数据；所述采集数据包括所述监测位置中漏油检测传感器采集的检测数据和图像采集设备采集的视频数据；根据多个检测数据中的异常数据,在多个监测位置中确定候选漏油位置；根据每个所述候选漏油位置的视频数据,在多个候选漏油位置中确定目标漏油位置；根据所述目标漏油位置,进行针对目标区域的漏油报警。</abstract>
  </patent>
  <patent id="37">
    <title>ESummary-主权项: 1.一种漏油检测的方法,其特征在于,包括：获取目标区域中每个监测位置的采集数据；所述采集数据包括所述监测位置中漏油检测传感器采集的检测数据和图像采集设备采集的视频数据；根据多个检测数据中的异常数据,在多个监测位置中确定候选漏油位置；根据每个所述候选漏油位置的视频数据,在多个候选漏油位置中确定目标漏油位置；根据所述目标漏油位置,进行针对目标区域的漏油报警。</title>
  </patent>
  <patent id="38">
    <title>数据代理装置、方法、电子设备、系统及存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请提供一种数据代理装置、方法、电子设备、系统及存储介质。其中装置包括：报文接收单元,用于接收多个终端设备发送的物联网报文,对接收到的物联网报文进行去重操作；报文识别单元,用于根据预设的验证规则和业务规则,验证去重后的物联网报文是否为噪音报文；报文解析单元,用于根据报文解析模板,对验证后的非噪音报文进行解析,得到解析后的数据,其中,不同类型的终端设备配置有不同的报文解析模板；数据融合单元,用于将不同终端设备对应的解析后的数据,和/或,同一终端设备在不同时段对应的解析后的数据进行融合；数据上报单元,用于将融合后的数据发送至物联网平台进行处理,可以减轻物联网平台处理数据的压力,提升处理效率。</abstract>
  </patent>
  <patent id="39">
    <title>一种含水量在线分析系统及方法</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
      <inventor>中国石油天然气集团有限公司</inventor>
    </inventors>
    <abstract>本申请提供了一种含水量在线分析系统及方法,该系统包括：设备参数采集子系统,用于采集设备实时运行参数,并将设备实时运行参数发送至含水量实时计算子系统和计算参数调节子系统；含水量实时计算子系统,用于对设备实时运行参数进行解析,得到实时工艺参数,并根据实时工艺参数计算实时含水量预测值,还用于接收计算参数调节子系统发送的调节参数,根据调节参数对含水量预测算法中对应的参数进行调节；计算参数调节子系统,用于接收设备参数采集子系统发送的设备实时运行参数,当监测到设备实时运行参数中任一参数变化值超出对应的预设阈值时,根据设备实时运行参数生成调节参数,并将调节参数发送至含水量实时计算子系统。</abstract>
  </patent>
  <patent id="40">
    <title>工业互联网网关及其组态方法</title>
    <inventors>
      <inventor>唐杰</inventor>
      <inventor>卫乾</inventor>
      <inventor>梁潇</inventor>
      <inventor>刘星</inventor>
    </inventors>
  </patent>
  <patent id="41">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="42">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本申请提供一种工业互联网网关及其组态方法。该方法包括：IO模块接收所述主控模块发送的第一位置信息；并根据第一位置信息获得第二位置信息；IO模块向下一IO模块发送第二位置信息,以使下一IO模块根据第二位置信息进行位置确定,直至最后一个IO模块确定位置信息为止；获取到第二位置信息的IO模块向主控模块发送注册信息以进行入网注册；主控模块在接收到最后一个IO模块发送的注册信息后确定组态完成。本申请实施例通过总线串联主控模块和IO模块,并利用IO总线传输位置信息,从而使得每个模块都能确定自身所在的位置,并将所在的位置注册到主控模块中,以实现自动组网,无需人工操作。</abstract>
  </patent>
  <patent id="43">
    <title>一种检测方法、装置、电子设备及计算机可读存储介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种检测方法、装置、电子设备及计算机可读存储介质,该方法包括：根据检测装置移动的起始时刻、起始位置、与水平方向的夹角、移动速度和每个检测点对应的检测时刻,确定每个检测点在目标管道上的位置；其中检测装置指向目标管道上该检测点所在横切面的中心且沿目标管道匀速移动；再根据检测装置向目标管道上每个检测点对应的外壁发射以及接收到第一检测信号的第一时长、向该检测点对应的内壁发射以及接收到第二检测信号的第二时长,判断该检测点是否为泄漏点；若是,则将该检测点的位置确定为泄漏点的位置。本申请中针对每个检测点,判断该检测点是否为泄漏点,进而确定泄漏点的位置,以提高检测的准确率和全面性。</abstract>
  </patent>
  <patent id="44">
    <title>一种摄像装置以及生产监控系统</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种摄像装置以及生产监控系统,其中,该摄像装置包括摄像头、处理器以及语音播放设备,所述处理器分别与所述摄像头和所述语音播放设备电连接；所述摄像头,用于采集目标摄像区域内的监控图像,并将所述监控图像发送给所述处理器；所述处理器,用于将接收到的所述监控图像输入到预先训练好的图像分析模型中,获取所述图像分析模型输出的图像分析结果,若所述图像分析结果为待预警结果,则查找与所述图像分析结果相匹配的第一语音信息,并将查找到的所述第一语音信息发送给所述语音播放设备；所述语音播放设备,用于对接收到的所述第一语音信息进行播放。本申请能够提高排查危险状况的准确率,提高监控的效率。</abstract>
  </patent>
  <patent id="45">
    <title>一种设备异常的检测系统、方法、计算机设备和介质</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种设备异常的检测系统、方法、计算机设备和介质,该系统包括：数据处理模块,用于获取目标设备在当前检测周期之前的历史检测数据；对所述历史检测数据进行筛选,确定出训练样本集合；算法训练模块,用于通过训练样本集合中的训练数据对待训练的异常检测算法进行训练；异常检测模块,用于获取所述目标设备在当前检测周期中的目标采集数据；将所述目标采集数据输入至训练好的所述异常检测算法,得到所述目标采集数据所对应的检测结果；异常分析模块,用于根据所述检测结果确定目标检测设备的异常解决措施；将所述异常解决措施进行展示。</abstract>
  </patent>
  <patent id="46">
    <title>数据处理器(AIoT-sensor)</title>
    <inventors>
      <inventor>周健臣</inventor>
      <inventor>杨剑锋</inventor>
      <inventor>刘旭鹏</inventor>
      <inventor>卫乾</inventor>
      <inventor>冯志勤</inventor>
      <inventor>赵艳江</inventor>
    </inventors>
  </patent>
  <patent id="47">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="48">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="49">
    <title>Summary-摘要: 1.本外观设计产品的名称：数据处理器(AIoT-sensor)。2.本外观设计产品的用途：用于数据采集和处理。3.本外观设计产品的设计要点：在于形状与图案的结合。4.最能表明设计要点的图片或照片：立体图。</title>
  </patent>
  <patent id="50">
    <title>便携式控制器(RF-AIoT-1000)</title>
    <inventors>
      <inventor>周健臣</inventor>
      <inventor>杨剑锋</inventor>
      <inventor>杜秋明</inventor>
      <inventor>卫乾</inventor>
      <inventor>赵艳江</inventor>
      <inventor>江捷</inventor>
    </inventors>
  </patent>
  <patent id="51">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="52">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="53">
    <title>Summary-摘要: 1.本外观设计产品的名称：便携式控制器(RF-AIoT-1000)。2.本外观设计产品的用途：用于数据的综合采集和分析,视频流处理。3.本外观设计产品的设计要点：在于形状与图案的结合。4.最能表明设计要点的图片或照片：立体图。</title>
  </patent>
  <patent id="54">
    <title>一种含水量在线分析系统及方法</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种含水量在线分析系统及方法,该系统包括：设备参数采集子系统,用于采集设备实时运行参数,并将设备实时运行参数发送至含水量实时计算子系统和计算参数调节子系统；含水量实时计算子系统,用于对设备实时运行参数进行解析,得到实时工艺参数,并根据实时工艺参数计算实时含水量预测值,还用于接收计算参数调节子系统发送的调节参数,根据调节参数对含水量预测算法中对应的参数进行调节；计算参数调节子系统,用于接收设备参数采集子系统发送的设备实时运行参数,当监测到设备实时运行参数中任一参数变化值超出对应的预设阈值时,根据设备实时运行参数生成调节参数,并将调节参数发送至含水量实时计算子系统。</abstract>
  </patent>
  <patent id="55">
    <title>一种预警方法、装置、电子设备及计算机可读存储介质</title>
    <inventors>
      <inventor>周健臣</inventor>
      <inventor>杨剑锋</inventor>
      <inventor>罗革新</inventor>
      <inventor>卫乾</inventor>
      <inventor>马政宇</inventor>
      <inventor>冯志勤</inventor>
    </inventors>
    <abstract>本申请实施例提供的一种预警方法、装置、电子设备及计算机可读存储介质,其中,该方法包括：根据获取的包含有静电桩和目标移动对象的目标视频数据,确定目标移动对象与静电桩的最近距离信息；在最近距离信息不大于第一预设值的情况下,判断目标移动对象的目标检测部位所在的检测框与静电桩所在的预测框的交并比是否大于第二预设值；若交并比大于第二预设值,则判断目标移动对象的目标检测部位与静电桩的连续接触时间是否小于预设时间；若连续接触时间小于预设时间,则对目标移动对象进行预警。通过自动判断目标移动对象是否触摸静电桩,起到预警的作用,以提高人员的安全性及监管过程中的自动化。</abstract>
  </patent>
  <patent id="56">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="57">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
    <abstract>1.本外观设计产品的名称：防爆摄像头(便携)。2.本外观设计产品的用途：用于人脸识别、工服识别,排查油气田现场潜在危险。3.本外观设计产品的设计要点：在于形状。4.最能表明设计要点的图片或照片：立体图。</abstract>
  </patent>
  <patent id="58">
    <title>一种原油含水量分析方法及装置</title>
    <inventors>
      <inventor>昆仑数智科技有限责任公司</inventor>
    </inventors>
    <abstract>本申请提供了一种原油含水量分析方法及装置,该方法包括：获取设备实时数据；所述设备实时数据包括稳定塔温度、稳定塔压力、稳前油进换热器压力和空气冷却器温度；将所述设备实时数据输入到原油含水量分析综合模型中,得到原油含水量实时预测值；所述原油含水量分析综合模型由原油含水量预测回归模型和数据判断模型。本申请实施例所提出的一种原油含水量分析方法,通过设备实时的工艺参数进行原油含水量的实时分析,并且采用的原油含水量分析综合模型中除原油含水量预测回归模型外,还包含有对输入和输出该综合模型的数据进行数据判断的数据判断模型,本申请实施例可有效降低原油含水量在线检测的误差,从而提高石油工业的生产效率。</abstract>
  </patent>
  <patent id="59">
    <title>ESummary-主权项: 1.一种原油含水量分析方法,其特征在于,包括：获取设备实时数据；所述设备实时数据包括稳定塔温度、稳定塔压力、稳前油进换热器压力和空气冷却器温度；将所述设备实时数据输入到原油含水量分析综合模型中,得到原油含水量实时预测值；所述原油含水量分析综合模型由原油含水量预测回归模型和数据判断模型。</title>
  </patent>
  <patent id="60">
    <title>三相计量设备通信方法、装置、三相计量设备及存储介质</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>卫乾</inventor>
      <inventor>刘星</inventor>
      <inventor>张亚楠</inventor>
      <inventor>陈冰</inventor>
      <inventor>唐杰</inventor>
    </inventors>
  </patent>
  <patent id="61">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="62">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本申请提供一种三相计量设备通信方法、装置、三相计量设备及存储介质,涉及油气生产数字化技术领域。三相计量设备为服务端,SCADA系统为客户端,应用于三相计量设备的方法包括：在接收到SCADA系统的连接请求时,向SCADA系统发送硬件证书,以使SCADA系统在硬件证书通过验证后,向三相计量设备发送密钥协商信息；基于SCADA系统发送的密钥协商信息确定第一加密密钥和第一解密密钥；在向SCADA系统发送三相流量数据时采用第一加密密钥对三相流量数据进行加密,在接收SCADA系统的数据时采用第一解密密钥对该数据解密。通过对实时油气水三相流量数据等进行加密传输,提高了三相计量设备的数据传输安全性。</abstract>
  </patent>
  <patent id="63">
    <title>一种石油化工生产现场安全合规性实时检测系统及方法</title>
    <inventors>
      <inventor>中国石油大学(北京)</inventor>
    </inventors>
    <abstract>本发明涉及一种石油化工生产现场安全合规性实时检测系统及方法,其步骤：初始化预先建立的深度学习模型的网络参数；判断读入文件格式并读取；文件读取成功后上传到深度学习模型中,处于待检测状态；将采集的石油化工生产现场图像信息传输至深度学习模型,加载深度学习模型以及各个目标已有的对应权重文件；针对摄像头采集的图像信息,利用深度学习模型进行计算并提取目标的位置和轮廓,若出现需要检测的事件和目标则将其框出并保存计算结果；判断检测过程是否结束,若结束则输出图像检测得到的所有结果。本发明能够高效准确地检测到现场不合规操作及危险物并报警,降低安全事故的发生率,节省人力成本。</abstract>
  </patent>
  <patent id="64">
    <title>一种抽油机生产参数优化方法</title>
    <inventors>
      <inventor>闫学峰</inventor>
      <inventor>檀朝琴</inventor>
      <inventor>乐小陶</inventor>
      <inventor>郭月明</inventor>
      <inventor>薛广民</inventor>
      <inventor>卫乾</inventor>
    </inventors>
  </patent>
  <patent id="65">
    <title>Source-文献来源: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="66">
    <title>Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本发明公开了一种抽油机生产参数优化方法,属于油气生产领域。该方法包括：获取实时生产参数；根据实时生产参数,对油井工况进行诊断,获取一个油田中可进行优化的油井；根据实时生产参数对可进行优化的油井的抽油机生产参数进行自动优化。本发明通过部署在油井现场的物联网设备获取实时生产参数,并通过部署在作业区生产管理平台上的诊断分析设备根据实时生产参数对油井工况进行诊断以获取油田中可进行优化的油井,而后根据实时生产参数对可进行优化的油井的抽油机生产参数进行自动优化,在抽油机生产参数优化过程中,无需通过人工调整参数,操作简单,且优化过程所需时间较短,生产效率较高。</abstract>
  </patent>
  <patent id="67">
    <title>工业互联网网关及其组态方法</title>
    <inventors>
      <inventor>唐杰</inventor>
      <inventor>卫乾</inventor>
      <inventor>梁潇</inventor>
      <inventor>刘星</inventor>
    </inventors>
  </patent>
  <patent id="68">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="69">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本申请提供一种工业互联网网关及其组态方法。该方法包括：IO模块接收所述主控模块发送的第一位置信息；并根据第一位置信息获得第二位置信息；IO模块向下一IO模块发送第二位置信息,以使下一IO模块根据第二位置信息进行位置确定,直至最后一个IO模块确定位置信息为止；获取到第二位置信息的IO模块向主控模块发送注册信息以进行入网注册；主控模块在接收到最后一个IO模块发送的注册信息后确定组态完成。本申请实施例通过总线串联主控模块和IO模块,并利用IO总线传输位置信息,从而使得每个模块都能确定自身所在的位置,并将所在的位置注册到主控模块中,以实现自动组网,无需人工操作。</abstract>
  </patent>
  <patent id="70">
    <title>对井场数据进行处理的方法和井口控制器</title>
    <inventors>
      <inventor>刘星</inventor>
      <inventor>卫乾</inventor>
      <inventor>伍儒彬</inventor>
      <inventor>朱润平</inventor>
    </inventors>
  </patent>
  <patent id="71">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
  </patent>
  <patent id="72">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
    <abstract>本发明公开了一种对井场数据进行处理的方法和井口控制器,属于石油采集技术领域。方法包括：实时通过油井的载荷传感器和角位移传感器,获取油井的抽油机的多组载荷数据,所述多组载荷数据中的每组载荷数据包括载荷值和位移值；根据所述多组载荷数据,生成所述抽油机的地面功图；根据所述地面功图,计算所述抽油机的泵冲满度；根据所述泵冲满度,计算所述抽油机的冲次；根据所述冲次,计算所述抽油机的最佳运行频率；获取所述抽油机的当前运行频率；根据所述当前运行频率和所述最佳运行频率,调整所述抽油机的当前运行频率。井口控制器包括：第一获取模块,第一生成模块,第一计算模块,第二计算模块,第三计算模块,第二获取模块和第一调整模块。</abstract>
  </patent>
  <patent id="73">
    <title>控制器的系统启动方法及装置</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>谢华峰</inventor>
      <inventor>卫乾</inventor>
    </inventors>
  </patent>
  <patent id="74">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
  </patent>
  <patent id="75">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
    <abstract>本发明公开了一种控制器的系统启动的方法及装置,属于油气生产自动控制领域。该方法包括：当检测到系统启动指令时,从存储的环境变量中获取第一存储地址和第二存储地址；基于第一存储地址,获取第一操作系统的数据,以及基于第二存储地址,获取第一文件系统的数据；基于第一操作系统的数据,对第一操作系统进行校验,基于第一文件系统的数据,对第一文件系统进行校验；当第一操作系统和第一文件系统均校验成功时,启动第一操作系统和第一文件系统。本发明实施例通过对第一操作系统和第一文件系统进行校验,从而当校验成功时可以确定第一操作系统和第一文件系统能够正常启动,进而保证控制器的稳定运行。</abstract>
  </patent>
  <patent id="76">
    <title>控制器的系统启动方法及装置</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>谢华峰</inventor>
      <inventor>卫乾</inventor>
    </inventors>
  </patent>
  <patent id="77">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
  </patent>
  <patent id="78">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
    <abstract>本发明公开了一种控制器的系统启动的方法及装置,属于油气生产自动控制领域。该方法包括：当检测到系统启动指令时,从存储的环境变量中获取第一存储地址和第二存储地址；基于第一存储地址,获取第一操作系统的数据,以及基于第二存储地址,获取第一文件系统的数据；基于第一操作系统的数据,对第一操作系统进行校验,基于第一文件系统的数据,对第一文件系统进行校验；当第一操作系统和第一文件系统均校验成功时,启动第一操作系统和第一文件系统。本发明实施例通过对第一操作系统和第一文件系统进行校验,从而当校验成功时可以确定第一操作系统和第一文件系统能够正常启动,进而保证控制器的稳定运行。</abstract>
  </patent>
  <patent id="79">
    <title>一种抽油机生产参数优化方法</title>
    <inventors>
      <inventor>闫学峰</inventor>
      <inventor>檀朝琴</inventor>
      <inventor>乐小陶</inventor>
      <inventor>郭月明</inventor>
      <inventor>薛广民</inventor>
      <inventor>卫乾</inventor>
    </inventors>
  </patent>
  <patent id="80">
    <title>Source-文献来源: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="81">
    <title>Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本发明公开了一种抽油机生产参数优化方法,属于油气生产领域。该方法包括：获取实时生产参数；根据实时生产参数,对油井工况进行诊断,获取一个油田中可进行优化的油井；根据实时生产参数对可进行优化的油井的抽油机生产参数进行自动优化。本发明通过部署在油井现场的物联网设备获取实时生产参数,并通过部署在作业区生产管理平台上的诊断分析设备根据实时生产参数对油井工况进行诊断以获取油田中可进行优化的油井,而后根据实时生产参数对可进行优化的油井的抽油机生产参数进行自动优化,在抽油机生产参数优化过程中,无需通过人工调整参数,操作简单,且优化过程所需时间较短,生产效率较高。</abstract>
  </patent>
  <patent id="82">
    <title>ESummary-主权项: 一种抽油机生产参数优化方法,其特征在于,所述方法包括：获取实时生产参数,所述实时生产参数通过部署在油井现场的物联网设备获取,包括油井工况参数和抽油机生产参数；根据所述实时生产参数,对油井工况进行诊断,获取一个油田中可进行优化的油井,所述油井工况通过部署在作业区生产管理平台上的诊断分析设备进行诊断；根据所述实时生产参数对所述可进行优化的油井的抽油机生产参数进行自动优化。</title>
  </patent>
  <patent id="83">
    <title>对井场数据进行处理的方法和井口控制器</title>
    <inventors>
      <inventor>刘星</inventor>
      <inventor>卫乾</inventor>
      <inventor>伍儒彬</inventor>
      <inventor>朱润平</inventor>
    </inventors>
  </patent>
  <patent id="84">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
  </patent>
  <patent id="85">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
    <abstract>本发明公开了一种对井场数据进行处理的方法和井口控制器,属于石油采集技术领域。方法包括：实时通过油井的载荷传感器和角位移传感器,获取油井的抽油机的多组载荷数据,所述多组载荷数据中的每组载荷数据包括载荷值和位移值；根据所述多组载荷数据,生成所述抽油机的地面功图；根据所述地面功图,计算所述抽油机的泵冲满度；根据所述泵冲满度,计算所述抽油机的冲次；根据所述冲次,计算所述抽油机的最佳运行频率；获取所述抽油机的当前运行频率；根据所述当前运行频率和所述最佳运行频率,调整所述抽油机的当前运行频率。井口控制器包括：第一获取模块,第一生成模块,第一计算模块,第二计算模块,第三计算模块,第二获取模块和第一调整模块。</abstract>
  </patent>
  <patent id="86">
    <title>风光互补控制逆变一体机</title>
    <inventors>
      <inventor>梁瑞科</inventor>
      <inventor>施大钟</inventor>
      <inventor>陈宝</inventor>
      <inventor>卫乾</inventor>
      <inventor>张洪垠</inventor>
      <inventor>施登宇</inventor>
    </inventors>
  </patent>
  <patent id="87">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;上海机易电站设备有限公司</title>
  </patent>
  <patent id="88">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;上海机易电站设备有限公司</title>
  </patent>
  <patent id="89">
    <title>Summary-摘要: 1．本外观设计产品的名称：风光互补控制逆变一体机；2．本外观设计产品的用途：本外观设计产品用于风光互补发电设备,利用风能和太阳能向用户提供稳定安全的电源；3．本外观设计产品的设计要点：在于本产品的整体形状；4．最能表明本外观设计设计要点的图片或照片：主视图。</title>
  </patent>
  <patent id="90">
    <title>数据传输终端及系统</title>
    <inventors>
      <inventor>刘淑君</inventor>
      <inventor>卫乾</inventor>
      <inventor>陈宝</inventor>
      <inventor>黄芳</inventor>
    </inventors>
  </patent>
  <patent id="91">
    <title>Source-文献来源: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
  </patent>
  <patent id="92">
    <title>Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>郭月明</inventor>
      <inventor>卫乾</inventor>
      <inventor>郭淳</inventor>
      <inventor>唐杰</inventor>
      <inventor>兰兰</inventor>
      <inventor>兰香</inventor>
      <inventor>刘淑君</inventor>
      <inventor>伍儒彬</inventor>
      <inventor>靳苗</inventor>
    </inventors>
    <abstract>本实用新型公开一种数据传输终端及系统,属于数据传输技术领域。该终端包括：GPRS模块、供电模块和稳定模块,GPRS模块集成有用于数据传输的数据传输子模块和用于数据处理的数据处理子模块；供电模块与GPRS模块连接,用于向GPRS模块供电；稳定模块分别与供电模块和GPRS模块连接,用于在GPRS模块出现异常时,控制供电模块停止向GPRS模块供电。本实用新型解决了现有技术中数据传输终端的电路结构复杂且成本较高的问题,达到了简化数据传输终端的电路结构以及降低成本的效果。本实用新型用于数据传输。</abstract>
  </patent>
  <patent id="93">
    <title>Source-文献来源: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="94">
    <title>Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本实用新型公开了一种天线及无线变送器,属于变送器领域。所述天线包括：天线本体和防护套,所述防护套的一端开口且所述防护套的另一端封闭,所述天线本体安装在所述防护套内,所述防护套上设有用于与所述无线变送器的壳体可拆卸连接的对接结构。所述无线变送器包括：变送器壳体、主电路板和天线,所述主电路板安装在所述变送器壳体的内部,所述天线为前述天线,所述天线通过所述对接结构与所述变送器壳体可拆卸连接。本实用新型解决了在将天线与变送器的壳体螺纹连接时带来的线缆断裂、变送器发生损坏的问题。此外,防护套还可以为天线本体提供保护。</abstract>
  </patent>
  <patent id="95">
    <title>一种显示装置</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>郭月明</inventor>
      <inventor>卫乾</inventor>
      <inventor>郭淳</inventor>
      <inventor>刘淑君</inventor>
      <inventor>伍儒彬</inventor>
      <inventor>兰兰</inventor>
      <inventor>兰香</inventor>
      <inventor>唐杰</inventor>
      <inventor>靳苗</inventor>
    </inventors>
  </patent>
  <patent id="96">
    <title>Source-文献来源: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="97">
    <title>Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
    <inventors>
      <inventor>马亮</inventor>
      <inventor>郭月明</inventor>
      <inventor>卫乾</inventor>
      <inventor>郭淳</inventor>
      <inventor>唐杰</inventor>
      <inventor>兰兰</inventor>
      <inventor>刘淑君</inventor>
      <inventor>兰香</inventor>
      <inventor>伍儒斌</inventor>
      <inventor>靳苗</inventor>
    </inventors>
    <abstract>本实用新型公开了一种显示装置,属于变送器领域。所述显示装置包括：显示模组,所述显示模组包括液晶显示屏和与所述液晶显示屏焊接的电路板,所述显示装置还包括保护壳,所述保护壳包括上壳体和与所述上壳体可拆卸连接的下壳体,所述上壳体与所述下壳体之间形成空腔,所述显示模组位于所述空腔中,所述上壳体上开设有窗口,所述显示模组的显示面露出所述窗口,所述下壳体上开设有线缆孔。本实用新型通过为显示模组设置保护壳,除显示模组的显示面露出保护壳之外,显示模组其他部分均在保护壳的内部,保护壳可以为显示模组提供防护,避免显示模组在独立存放时,电路板底部的电气部件受到损坏。</abstract>
  </patent>
  <patent id="98">
    <title>Source-文献来源: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
  </patent>
  <patent id="99">
    <title>Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司</title>
    <abstract>本实用新型公开了一种无线示功仪,属于油田采油技术领域。所述无线示功仪包括壳体、载荷传感器、电路板、发射天线、以及电池,电路板上设有通信电路和控制电路,控制电路分别与载荷传感器、通信电路、电池电连接,通信电路分别与发射天线、电池电连接,壳体包括依次密封连接的不锈钢底座、中间腔体、盖体、以及天线罩,载荷传感器设置在不锈钢底座的内部,电路板设置在中间腔体的内部,电池设置在盖体的内部,发射天线设置在天线罩的内部,中间腔体、盖体和天线罩均为ABS塑料壳。本实用新型采用不锈钢底座和ABS塑料壳组成壳体,一方面可以降低整个无线示功仪的重量,另一方面还可以提高数据传输的质量。</abstract>
  </patent>
</patents>
