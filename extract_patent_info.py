#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利信息提取脚本
从.docx文件中提取专利信息并结构化处理
"""

import os
import re
import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
from docx import Document
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatentExtractor:
    def __init__(self):
        self.patents = []
        
    def extract_from_docx(self, file_path):
        """从docx文件中提取专利信息"""
        try:
            doc = Document(file_path)
            logger.info(f"开始处理文件: {file_path}")
            
            current_patent = {}
            text_content = []
            
            # 收集所有文本内容
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    text_content.append(text)
            
            # 合并所有文本用于分析
            full_text = '\n'.join(text_content)
            
            # 使用正则表达式提取专利信息
            self.parse_patent_text(full_text)
            
            logger.info(f"成功提取 {len(self.patents)} 项专利信息")
            return self.patents
            
        except Exception as e:
            logger.error(f"处理文件时出错: {e}")
            return []
    
    def parse_patent_text(self, text):
        """解析专利文本信息"""
        # 按行分割文本
        lines = text.split('\n')
        current_patent = {}
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 检测专利标题（通常是较长的描述性文本）
            if self.is_patent_title(line):
                if current_patent:  # 保存前一个专利
                    self.patents.append(current_patent)
                current_patent = {'title': line}
                continue
            
            # 提取专利号
            patent_number = self.extract_patent_number(line)
            if patent_number and 'patent_number' not in current_patent:
                current_patent['patent_number'] = patent_number
                continue
            
            # 提取日期信息
            date_info = self.extract_dates(line)
            if date_info:
                current_patent.update(date_info)
                continue
            
            # 提取发明人信息
            inventors = self.extract_inventors(line)
            if inventors:
                current_patent['inventors'] = inventors
                continue
            
            # 提取技术领域或分类信息
            tech_field = self.extract_tech_field(line)
            if tech_field:
                current_patent['tech_field'] = tech_field
                continue
            
            # 提取摘要或技术要点
            if self.is_abstract_or_summary(line):
                current_patent['abstract'] = line
                continue
        
        # 添加最后一个专利
        if current_patent:
            self.patents.append(current_patent)
    
    def is_patent_title(self, line):
        """判断是否为专利标题"""
        # 专利标题通常较长，包含技术术语
        if len(line) > 10 and len(line) < 200:
            # 包含常见的技术关键词
            tech_keywords = ['方法', '系统', '装置', '设备', '技术', '工艺', '算法', '模型', '检测', '控制', '监测', '分析', '处理', '优化']
            return any(keyword in line for keyword in tech_keywords)
        return False
    
    def extract_patent_number(self, line):
        """提取专利号"""
        # 中国专利号格式：CN + 数字 + 字母
        patterns = [
            r'CN\d{9}[A-Z]',  # CN123456789A
            r'ZL\d{4}\d{8}\.\d',  # ZL201812345678.9
            r'\d{4}\d{8}\.\d',  # 201812345678.9
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group()
        return None
    
    def extract_dates(self, line):
        """提取日期信息"""
        date_info = {}
        
        # 申请日期
        apply_patterns = [
            r'申请日[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'申请日期[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'申请时间[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
        ]
        
        for pattern in apply_patterns:
            match = re.search(pattern, line)
            if match:
                date_info['apply_date'] = match.group(1)
                break
        
        # 授权日期
        grant_patterns = [
            r'授权日[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'授权日期[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'授权时间[：:]\s*(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
        ]
        
        for pattern in grant_patterns:
            match = re.search(pattern, line)
            if match:
                date_info['grant_date'] = match.group(1)
                break
        
        return date_info
    
    def extract_inventors(self, line):
        """提取发明人信息"""
        inventor_patterns = [
            r'发明人[：:]\s*(.+)',
            r'发明者[：:]\s*(.+)',
            r'申请人[：:]\s*(.+)',
        ]
        
        for pattern in inventor_patterns:
            match = re.search(pattern, line)
            if match:
                inventors_str = match.group(1)
                # 分割多个发明人
                inventors = [name.strip() for name in re.split('[;；,，]', inventors_str) if name.strip()]
                return inventors
        return None
    
    def extract_tech_field(self, line):
        """提取技术领域"""
        field_patterns = [
            r'技术领域[：:]\s*(.+)',
            r'分类号[：:]\s*(.+)',
            r'IPC分类[：:]\s*(.+)',
        ]
        
        for pattern in field_patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1).strip()
        return None
    
    def is_abstract_or_summary(self, line):
        """判断是否为摘要或技术要点"""
        abstract_keywords = ['摘要', '技术要点', '主要内容', '发明内容']
        return any(keyword in line for keyword in abstract_keywords) and len(line) > 20

    def generate_xml(self, output_file='patents_info.xml'):
        """生成XML文件"""
        root = ET.Element('patents')
        root.set('total_count', str(len(self.patents)))
        root.set('generated_date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        for i, patent in enumerate(self.patents, 1):
            patent_elem = ET.SubElement(root, 'patent')
            patent_elem.set('id', str(i))

            # 专利标题
            title = patent.get('title', '').replace('Title-专利名称: ', '')
            if title:
                title_elem = ET.SubElement(patent_elem, 'title')
                title_elem.text = title

            # 专利号
            if 'patent_number' in patent:
                number_elem = ET.SubElement(patent_elem, 'patent_number')
                number_elem.text = patent['patent_number']

            # 申请日期
            if 'apply_date' in patent:
                apply_date_elem = ET.SubElement(patent_elem, 'apply_date')
                apply_date_elem.text = patent['apply_date']

            # 授权日期
            if 'grant_date' in patent:
                grant_date_elem = ET.SubElement(patent_elem, 'grant_date')
                grant_date_elem.text = patent['grant_date']

            # 发明人
            if 'inventors' in patent and patent['inventors']:
                inventors_elem = ET.SubElement(patent_elem, 'inventors')
                for inventor in patent['inventors']:
                    inventor_elem = ET.SubElement(inventors_elem, 'inventor')
                    inventor_elem.text = inventor

            # 技术领域
            if 'tech_field' in patent:
                field_elem = ET.SubElement(patent_elem, 'tech_field')
                field_elem.text = patent['tech_field']

            # 摘要
            abstract = patent.get('abstract', '').replace('Summary-摘要: ', '')
            if abstract:
                abstract_elem = ET.SubElement(patent_elem, 'abstract')
                abstract_elem.text = abstract

        # 格式化XML
        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")

        # 保存XML文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)

        logger.info(f"XML文件已生成: {output_file}")
        return output_file

def main():
    """主函数"""
    extractor = PatentExtractor()

    # 查找所有.docx文件
    docx_files = [f for f in os.listdir('.') if f.endswith('.docx') and not f.startswith('~$')]

    if not docx_files:
        logger.error("未找到.docx文件")
        return

    # 处理每个文件
    for file_path in docx_files:
        logger.info(f"处理文件: {file_path}")
        patents = extractor.extract_from_docx(file_path)

    # 保存结果为JSON（用于调试）
    with open('patents_debug.json', 'w', encoding='utf-8') as f:
        json.dump(extractor.patents, f, ensure_ascii=False, indent=2)

    # 生成XML文件
    xml_file = extractor.generate_xml()

    logger.info(f"专利信息提取完成，共提取 {len(extractor.patents)} 项专利")
    logger.info(f"XML文件已生成: {xml_file}")
    return extractor.patents

if __name__ == "__main__":
    main()
