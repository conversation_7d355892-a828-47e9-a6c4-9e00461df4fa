#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
论文成果与科学问题关联分析生成器
为论文与科学问题关系生成多种类型的可视化图表
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class PaperAnalysisGenerator:
    def __init__(self):
        # 科学问题与论文数据
        self.scientific_problems = {
            'C1': {
                'id': 'C1',
                'title': '面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究',
                'short_title': '多模态大语言模型协同架构',
                'papers': [
                    {
                        'title': '"十四五"油气上游业务数智化转型实践与展望',
                        'authors': '马涛; 王铁成; 卫乾',
                        'journal': '石油科技论坛',
                        'year': 2025,
                        'publication_date': '2025-06-25',
                        'keywords': ['油气业务', '数智化转型', '湖仓化', '平台化', '生态化', '能力中台'],
                        'abstract': '针对油气业务信息化过程中存在的数据库多、平台多、孤立应用多和数据共享难、技术复用难、应用开发难、业务协同难等瓶颈问题，以构建"数据湖+云平台+应用场景"敏捷应用为目标，探索了"数据湖仓化、技术平台化、能力中台化、运营生态化"的发展模式...',
                        'relevance': '提出数据湖仓化和能力中台化发展模式，构建大语言模型协同架构的基础设施',
                        'strength': '强相关',
                        'score': 9.0,
                        'impact_factor': 8.5
                    },
                    {
                        'title': '油气勘探开发领域知识图谱构建及应用探讨',
                        'authors': '徐寅; 卫乾; 赵世亮; 熊伟; 王洋洋; 刘斌; 庞资胜; 赵启蒙; 许子君',
                        'journal': '石油科技论坛',
                        'year': 2023,
                        'publication_date': '2023-12-25',
                        'keywords': ['油气勘探开发', '知识图谱', '业务模型', '智能算法'],
                        'abstract': '建立一套以业务模型为核心的自顶向下的石油勘探开发知识图谱领域本体构建的技术方法，结合实际业务需求，探索了知识图谱结合人工智能算法在油气勘探开发业务中的知识检索、知识推理和知识评价三类应用场景...',
                        'relevance': '构建油气领域知识图谱，为多模态大语言模型提供结构化知识支撑',
                        'strength': '强相关',
                        'score': 8.5,
                        'impact_factor': 8.5
                    },
                    {
                        'title': '基于油气勘探小数据集的知识图谱表示与融合',
                        'authors': '田芷瑜; 卫乾; 赵世亮; 许野',
                        'journal': '信息与电脑(理论版)',
                        'year': 2023,
                        'publication_date': '2023-10-10',
                        'keywords': ['知识表示', '知识融合', '知识图谱', '油气勘探', '无监督学习'],
                        'abstract': '提出一种适用于油气勘探的小数据集的知识表示和融合方法，采用数据库和网络本体语言(OWL)相结合的方法进行图谱表示，让多种结构的数据可以用统一的方式相结合...',
                        'relevance': '解决小数据集下的知识融合问题，优化多模态模型的数据处理能力',
                        'strength': '中等相关',
                        'score': 7.0,
                        'impact_factor': 6.5
                    },
                    {
                        'title': '基于知识图谱的水波网络智能推荐算法',
                        'authors': '白梅; 卫乾; 赵世亮; 徐寅',
                        'journal': '石油知识',
                        'year': 2024,
                        'publication_date': '2024-09-15',
                        'keywords': ['知识图谱', '智能推荐', '水波网络', '算法优化'],
                        'abstract': '建成世界一流数字化智能化共享平台，即勘探开发梦想云...',
                        'relevance': '实现智能化推荐算法，支撑多模态模型的动态优化机制',
                        'strength': '中等相关',
                        'score': 6.5,
                        'impact_factor': 6.0
                    }
                ]
            },
            'C2': {
                'id': 'C2',
                'title': '面向油气生产物联网的时序数据轻量化对象建模与源头质量协同控制方法',
                'short_title': '时序数据轻量化建模与质量控制',
                'papers': [
                    {
                        'title': '油田地面生产过程全生命周期多源碳耦合机制与减排路径',
                        'authors': '卫乾; 李国彬; 王伟; 王腾飞; 徐寅; 赵东亚',
                        'journal': '石油工程建设',
                        'year': 2025,
                        'publication_date': '2025-04-17',
                        'keywords': ['碳排放', '全生命周期', '机理模型', '减排措施', '油田地面生产过程'],
                        'abstract': '构建了油田地面生产系统的碳排放机理模型，系统解析了采油、集输、处理、储存及运输等核心环节的碳素代谢规律，综合考虑了不同生产阶段和核算方法的差异性...',
                        'relevance': '建立全生命周期数据模型，实现源头碳数据质量协同控制',
                        'strength': '强相关',
                        'score': 8.8,
                        'impact_factor': 8.0
                    },
                    {
                        'title': '基于时序预测算法进行煤层气井卡泵预警',
                        'authors': '高飞翔; 卫乾; 赵世亮; 庞资胜; 晋帅',
                        'journal': '石油知识',
                        'year': 2024,
                        'publication_date': '2024-11-15',
                        'keywords': ['时序预测', '卡泵预警', '机器学习', '故障诊断'],
                        'abstract': '提出使用时间序列预测算法对泵生产指标进行趋势预测，并通过构建机器学习算法模型实现对卡泵工况的预警...',
                        'relevance': '使用时序预测算法实现设备故障预警，保障源头数据质量',
                        'strength': '强相关',
                        'score': 8.5,
                        'impact_factor': 6.0
                    },
                    {
                        'title': '结合随钻声波与密度测井的孔隙压力预测方法',
                        'authors': '王铁成; 卫乾; 熊伟; 王强强; 张宁俊; 史亚会; 杨兵; 王新华',
                        'journal': '应用声学',
                        'year': 2025,
                        'publication_date': '2024-09-24',
                        'keywords': ['地层压力', '随钻监测', '随钻声波测井', '随钻密度测井', '神经网络'],
                        'abstract': '基于声波测井地层压力评价方法及密度测井地层压力评价方法，结合广义回归神经网络模型，构建了一种新的基于多随钻测井数据的地层压力监测方法...',
                        'relevance': '多源传感器数据融合的轻量化建模方法',
                        'strength': '中等相关',
                        'score': 7.5,
                        'impact_factor': 7.8
                    }
                ]
            },
            'C3': {
                'id': 'C3',
                'title': '油气生产物联网多智能体自组态工作流建模方法',
                'short_title': '多智能体自组态工作流建模',
                'papers': [
                    {
                        'title': '面向虚拟巡检的数字孪生可视分析系统',
                        'authors': '卫乾; 杜秋明; 马凯蒂; 林轶; 陈逸飞',
                        'journal': '电气时代',
                        'year': 2024,
                        'publication_date': '2024-08-10',
                        'keywords': ['数字孪生', '虚拟巡检', '智能路由', '可视分析'],
                        'abstract': '提出基于数字孪生技术的智能巡检方法，结合路网构建、时序数据预测和智能路由等算法，开发设计了一款数字孪生智能巡检编辑器，帮助用户快速搭建面向智能巡检的仿真应用场景...',
                        'relevance': '实现智能体协同的巡检工作流建模与仿真',
                        'strength': '强相关',
                        'score': 9.0,
                        'impact_factor': 7.5
                    },
                    {
                        'title': '油气藏-井筒-管网一体化耦合模拟方法研究',
                        'authors': '石海磊; 赵迎; 杨勇; 卫乾; 马良乾; 王铁成',
                        'journal': '中国石油和化工标准与质量',
                        'year': 2023,
                        'publication_date': '2023-05-31',
                        'keywords': ['智慧油气田', '生产一体化', '耦合模拟', '数值模拟'],
                        'abstract': '建立一种将油气藏数值模型、井筒模型以及地面管网模型耦合到一起联合模拟方法，该方法能够成功在3个专业模型之间实现联动计算、数据流转，形成全生产流程一体化模拟...',
                        'relevance': '多智能体系统协同的工作流建模方法',
                        'strength': '强相关',
                        'score': 8.5,
                        'impact_factor': 6.5
                    },
                    {
                        'title': '油田成本措施事前算赢智能分析及应用',
                        'authors': '赵迎; 张燕; 卫乾; 杨勇; 马良乾',
                        'journal': '中国石油和化工标准与质量',
                        'year': 2023,
                        'publication_date': '2023-05-31',
                        'keywords': ['油藏经营管理', '效益评价', '措施评价', '信息化', '智能化'],
                        'abstract': '研究油田增产性措施事前算赢分析模型，综合考虑措施增量、油价、递减率、措施成本等因素，结合层次分析法(AHP)等决策算法应用...',
                        'relevance': '智能决策算法支持的自组态工作流优化',
                        'strength': '中等相关',
                        'score': 7.0,
                        'impact_factor': 6.5
                    },
                    {
                        'title': '基于多约束条件的弹性经济配产方法研究',
                        'authors': '赵迎; 卫乾; 陆光辉; 张燕; 石海磊',
                        'journal': '中国石油和化工标准与质量',
                        'year': 2023,
                        'publication_date': '2023-11-30',
                        'keywords': ['弹性管理', '产量预测', '成本预测', '约束条件', '效益配产'],
                        'abstract': '建立以油价波动弹性安排产量的运行决策机制，科学、合理地进行产量预测和成本预测，实现产量最大、效益最大、盈亏平衡及目标利润多种约束条件下的效益配产...',
                        'relevance': '多约束条件下的智能工作流动态优化',
                        'strength': '中等相关',
                        'score': 6.8,
                        'impact_factor': 6.5
                    },
                    {
                        'title': '油气藏合理配产的研究现状与展望',
                        'authors': '赵迎; 杨炳森; 卫乾; 马良乾; 王娟',
                        'journal': '中国石油和化工标准与质量',
                        'year': 2023,
                        'publication_date': '2023-06-15',
                        'keywords': ['配产', '优化算法', '一体化模型'],
                        'abstract': '通过将油气藏、井筒和地面管网无缝链接在一起构建具有综合模拟与优化能力的一体化生产系统，同时结合启发式优化算法等数学优化方法...',
                        'relevance': '多系统协同的一体化工作流建模方法',
                        'strength': '中等相关',
                        'score': 6.5,
                        'impact_factor': 6.5
                    },
                    {
                        'title': '油气田投资成本一体化智能分析及优化模型',
                        'authors': '赵迎; 任波; 卫乾; 王铁成; 张燕; 石海磊',
                        'journal': '中国石油和化工标准与质量',
                        'year': 2023,
                        'publication_date': '2023-12-15',
                        'keywords': ['油气田', '投资结构', '投资组合', '措施成本', '智能化', '模型'],
                        'abstract': '构建了油气田投资成本一体化智能分析及优化计算机算法模型，为油气田投资与成本规划提供了快速、准确的决策工具支持...',
                        'relevance': '智能优化模型支持的自组态决策工作流',
                        'strength': '中等相关',
                        'score': 6.2,
                        'impact_factor': 6.5
                    }
                ]
            }
        }
        
        # 颜色配置
        self.colors = {
            'C1': '#FF6B6B',  # 红色
            'C2': '#4ECDC4',  # 青色
            'C3': '#45B7D1',  # 蓝色
            '强': '#E74C3C',
            '中等': '#F39C12',
            '弱': '#95A5A6',
            'strong': '#E74C3C',
            'medium': '#F39C12',
            'weak': '#95A5A6'
        }

    def create_paper_sankey_chart(self, problem_id):
        """创建论文桑基图 - 展示论文与科学问题的支撑关系"""
        problem_data = self.scientific_problems[problem_id]
        papers = problem_data['papers']
        problem_title = problem_data['title']

        # 准备节点数据
        node_labels = []
        node_colors = []

        # 添加论文节点（左侧）
        for paper in papers:
            # 简化论文标题显示
            title = paper['title']
            if len(title) > 25:
                title = title[:22] + "..."
            node_labels.append(title)

            # 根据关联强度设置节点颜色
            strength = paper['strength']
            node_colors.append(self.colors[strength.replace('相关', '')])

        # 添加科学问题节点（右侧）
        problem_display_names = {
            'C1': "多模态大语言模型\n协同架构与动态优化",
            'C2': "时序数据轻量化\n对象建模与质量控制",
            'C3': "多智能体自组态\n工作流建模方法"
        }
        problem_display = problem_display_names.get(problem_id, problem_title[:20] + "...")
        node_labels.append(problem_display)
        node_colors.append('#2E8B57')  # 深绿色

        # 准备连接数据
        source_indices = list(range(len(papers)))
        target_indices = [len(papers)] * len(papers)
        values = []
        link_colors = []

        for paper in papers:
            strength = paper['strength']
            weight = 3 if strength == '强相关' else 2 if strength == '中等相关' else 1
            values.append(weight)
            link_colors.append(self.colors[strength.replace('相关', '')])

        # 创建桑基图
        fig = go.Figure(data=[go.Sankey(
            arrangement="snap",
            node=dict(
                pad=20,
                thickness=25,
                line=dict(color="rgba(0,0,0,0.3)", width=1),
                label=node_labels,
                color=node_colors,
                x=[0.1] * len(papers) + [0.9],  # 论文在左侧，科学问题在右侧
                y=[i/(len(papers)-1) for i in range(len(papers))] + [0.5]  # 垂直分布
            ),
            link=dict(
                source=source_indices,
                target=target_indices,
                value=values,
                color=link_colors,
                hovertemplate='<b>%{source.label}</b><br>' +
                             '→ <b>%{target.label}</b><br>' +
                             '关联强度: %{customdata[0]}<br>' +
                             '影响因子: %{customdata[1]}<br>' +
                             '发表年份: %{customdata[2]}<br>' +
                             '期刊: %{customdata[3]}<extra></extra>',
                customdata=[[paper['strength'], paper['impact_factor'],
                           paper['year'], paper['journal']]
                          for paper in papers]
            )
        )])

        # 设置图表布局
        fig.update_layout(
            title={
                'text': f"<b>论文成果支撑关系桑基图</b><br><span style='font-size:14px;color:#666'>{problem_title}</span>",
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 18, 'family': 'Microsoft YaHei, Arial, sans-serif'}
            },
            font=dict(size=11, family='Microsoft YaHei, Arial, sans-serif'),
            width=1400,
            height=700,
            margin=dict(l=20, r=20, t=120, b=50),
            paper_bgcolor='rgba(248,249,250,1)',
            plot_bgcolor='rgba(255,255,255,1)'
        )

        return fig

    def create_timeline_chart(self, problem_id):
        """创建时间线图 - 展示论文发表的时间趋势"""
        problem_data = self.scientific_problems[problem_id]
        papers = problem_data['papers']

        # 按年份排序
        sorted_papers = sorted(papers, key=lambda x: x['year'])

        fig = go.Figure()

        # 添加时间线
        fig.add_trace(go.Scatter(
            x=[p['year'] for p in sorted_papers],
            y=[p['score'] for p in sorted_papers],
            mode='markers+lines+text',
            text=[p['title'][:15] + '...' for p in sorted_papers],
            textposition="top center",
            marker=dict(
                size=[p['impact_factor'] * 3 for p in sorted_papers],
                color=[self.colors[p['strength'].replace('相关', '')] for p in sorted_papers],
                line=dict(width=2, color='white')
            ),
            line=dict(width=3, color=self.colors[problem_id]),
            hovertemplate='<b>%{text}</b><br>' +
                         '发表年份: %{x}<br>' +
                         '关联分数: %{y}<br>' +
                         '影响因子: %{customdata[0]}<br>' +
                         '期刊: %{customdata[1]}<extra></extra>',
            customdata=[[p['impact_factor'], p['journal']] for p in sorted_papers],
            name="论文发表趋势"
        ))

        fig.update_layout(
            title=f"论文发表时间线图<br><sub>{problem_data['short_title']}</sub>",
            xaxis_title="发表年份",
            yaxis_title="关联分数",
            font=dict(family='Microsoft YaHei', size=12),
            hovermode='closest',
            showlegend=False
        )

        return fig

    def create_journal_distribution(self, problem_id):
        """创建期刊分布图 - 展示论文在不同期刊的分布"""
        problem_data = self.scientific_problems[problem_id]
        papers = problem_data['papers']

        # 统计期刊分布
        journal_counts = {}
        journal_scores = {}
        for paper in papers:
            journal = paper['journal']
            journal_counts[journal] = journal_counts.get(journal, 0) + 1
            if journal not in journal_scores:
                journal_scores[journal] = []
            journal_scores[journal].append(paper['score'])

        # 计算平均分数
        journal_avg_scores = {j: sum(scores)/len(scores) for j, scores in journal_scores.items()}

        fig = go.Figure()

        # 添加柱状图
        fig.add_trace(go.Bar(
            x=list(journal_counts.keys()),
            y=list(journal_counts.values()),
            marker_color=[self.colors[problem_id]] * len(journal_counts),
            text=[f"平均分: {journal_avg_scores[j]:.1f}" for j in journal_counts.keys()],
            textposition='auto',
            hovertemplate='<b>%{x}</b><br>' +
                         '论文数量: %{y}<br>' +
                         '平均关联分数: %{customdata:.1f}<extra></extra>',
            customdata=list(journal_avg_scores.values())
        ))

        fig.update_layout(
            title=f"期刊分布图<br><sub>{problem_data['short_title']}</sub>",
            xaxis_title="期刊名称",
            yaxis_title="论文数量",
            font=dict(family='Microsoft YaHei', size=12),
            showlegend=False
        )

        return fig

    def create_keyword_analysis(self, problem_id):
        """创建关键词分析图 - 展示研究热点"""
        problem_data = self.scientific_problems[problem_id]
        papers = problem_data['papers']

        # 统计关键词频率
        keyword_counts = {}
        for paper in papers:
            for keyword in paper['keywords']:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

        # 取前10个关键词
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        fig = go.Figure()

        # 添加水平柱状图
        fig.add_trace(go.Bar(
            x=[count for _, count in top_keywords],
            y=[keyword for keyword, _ in top_keywords],
            orientation='h',
            marker_color=self.colors[problem_id],
            text=[str(count) for _, count in top_keywords],
            textposition='auto'
        ))

        fig.update_layout(
            title=f"研究热点关键词分析<br><sub>{problem_data['short_title']}</sub>",
            xaxis_title="出现频次",
            yaxis_title="关键词",
            font=dict(family='Microsoft YaHei', size=12),
            showlegend=False,
            height=600
        )

        return fig

    def get_enhanced_css(self):
        """获取增强版CSS样式"""
        return """
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .dashboard-container {
                max-width: 1600px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                backdrop-filter: blur(10px);
            }

            .header-section {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }

            .main-title {
                font-size: 2.5em;
                font-weight: 300;
                margin-bottom: 10px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .subtitle {
                font-size: 1.2em;
                opacity: 0.9;
                font-weight: 300;
            }

            .chart-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 30px;
                padding: 40px;
            }

            .chart-card {
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .chart-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            }

            .chart-title {
                font-size: 1.5em;
                color: #2c3e50;
                margin-bottom: 20px;
                text-align: center;
                font-weight: 600;
            }

            .chart-description {
                color: #7f8c8d;
                font-size: 0.9em;
                margin-bottom: 20px;
                text-align: center;
                line-height: 1.5;
            }

            .footer-section {
                background: #ecf0f1;
                padding: 30px 40px;
                text-align: center;
                border-top: 1px solid #bdc3c7;
            }

            .generation-info {
                color: #7f8c8d;
                font-size: 0.9em;
            }

            @media (max-width: 768px) {
                .main-title {
                    font-size: 2em;
                }

                .dashboard-container {
                    margin: 10px;
                    border-radius: 15px;
                }

                .chart-grid {
                    padding: 20px;
                    gap: 20px;
                }
            }
        </style>
        """

    def generate_paper_analysis_html(self, problem_id):
        """生成论文分析HTML文件"""
        problem_data = self.scientific_problems[problem_id]

        # 创建各种图表
        charts = {
            'sankey': self.create_paper_sankey_chart(problem_id),
            'timeline': self.create_timeline_chart(problem_id),
            'journal': self.create_journal_distribution(problem_id),
            'keywords': self.create_keyword_analysis(problem_id)
        }

        # 图表描述
        chart_descriptions = {
            'sankey': '桑基图展示了论文成果与科学问题之间的支撑关系，流向粗细表示关联强度。',
            'timeline': '时间线图展示了论文发表的时间趋势和研究发展脉络。',
            'journal': '期刊分布图展示了论文在不同期刊的发表情况和平均关联分数。',
            'keywords': '关键词分析图展示了研究领域的热点词汇和发展方向。'
        }

        # 图表标题
        chart_titles = {
            'sankey': '论文成果支撑关系桑基图',
            'timeline': '论文发表时间线图',
            'journal': '期刊分布分析图',
            'keywords': '研究热点关键词分析'
        }

        # 生成HTML内容
        css_styles = self.get_enhanced_css()

        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文成果与科学问题关联分析 - {problem_data['short_title']}</title>
    {css_styles}
</head>
<body>
    <div class="dashboard-container">
        <div class="header-section">
            <h1 class="main-title">论文成果与科学问题关联分析</h1>
            <p class="subtitle">{problem_data['title']}</p>
        </div>

        <div class="chart-grid">
"""

        # 添加每个图表
        for chart_key, fig in charts.items():
            chart_html = fig.to_html(include_plotlyjs=True, div_id=f"{chart_key}-plot")
            html_content += f"""
            <div class="chart-card">
                <h2 class="chart-title">{chart_titles[chart_key]}</h2>
                <p class="chart-description">{chart_descriptions[chart_key]}</p>
                <div id="{chart_key}-plot">
                    {chart_html}
                </div>
            </div>
"""

        html_content += f"""
        </div>

        <div class="footer-section">
            <div class="generation-info">
                <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                <p><strong>科学问题编号:</strong> {problem_id}</p>
                <p><strong>相关论文数量:</strong> {len(problem_data['papers'])} 篇</p>
                <p><strong>可视化图表数量:</strong> {len(charts)} 个</p>
            </div>
        </div>
    </div>
</body>
</html>"""

        return html_content

    def save_paper_analysis_html(self, problem_id):
        """保存论文分析HTML文件"""
        html_content = self.generate_paper_analysis_html(problem_id)
        filename = f'paper_analysis_{problem_id}.html'

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"论文分析HTML文件已保存: {filename}")
        return filename


def main():
    """主函数"""
    generator = PaperAnalysisGenerator()

    logger.info("开始生成论文成果关联分析...")

    generated_files = []

    # 为每个科学问题生成论文分析可视化
    for problem_id in ['C1', 'C2', 'C3']:
        logger.info(f"正在生成科学问题 {problem_id} 的论文分析...")

        try:
            output_file = generator.save_paper_analysis_html(problem_id)
            generated_files.append(output_file)

            problem_data = generator.scientific_problems[problem_id]
            papers = problem_data['papers']
            strong_count = sum(1 for p in papers if p['strength'] == '强相关')
            medium_count = sum(1 for p in papers if p['strength'] == '中等相关')

            print(f"\n=== 科学问题 {problem_id} ===")
            print(f"问题标题: {problem_data['title']}")
            print(f"输出文件: {output_file}")
            print(f"相关论文数量: {len(papers)} 篇")
            print(f"强相关论文: {strong_count} 篇")
            print(f"中等相关论文: {medium_count} 篇")

        except Exception as e:
            logger.error(f"生成科学问题 {problem_id} 的论文分析时出错: {e}")

    print(f"\n=== 所有论文分析可视化生成完成 ===")
    print(f"共生成 {len(generated_files)} 个HTML文件：")
    for filename in generated_files:
        print(f"  - {filename}")

    print(f"\n=== 图表类型说明 ===")
    print("1. 桑基图 - 展示论文与科学问题的支撑关系")
    print("2. 时间线图 - 展示论文发表的时间趋势")
    print("3. 期刊分布图 - 展示论文在不同期刊的分布")
    print("4. 关键词分析图 - 展示研究热点和发展方向")


if __name__ == "__main__":
    main()
