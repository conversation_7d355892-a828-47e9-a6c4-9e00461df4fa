#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多图表可视化生成器
为专利与科学问题关系生成多种类型的可视化图表
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MultiChartGenerator:
    def __init__(self):
        # 科学问题数据
        self.scientific_problems = {
            'C1': {
                'id': 'C1',
                'title': '面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究',
                'short_title': '多模态大语言模型协同架构',
                'patents': [
                    {
                        'title': '一种油气物联网的管理系统、管理方法、电子设备及介质',
                        'inventors': '刘星; 卫乾; 赵亮; 孙永光',
                        'application_date': '2025',
                        'publication_date': '2025-06-20',
                        'relevance': 'Focuses on IoT management system with data fusion and transmission optimization',
                        'strength': '强相关',
                        'score': 8.5,
                        'keywords': ['物联网', '数据融合', '传输优化', '管理系统']
                    },
                    {
                        'title': '一种面向油气生产物联网控制系统的自组态建模方法',
                        'inventors': '卫乾; 孙红军; 张明; 魏靖雯',
                        'application_date': '2024',
                        'publication_date': '2024-07-16',
                        'relevance': 'Proposes self-configuration modeling for IoT control systems',
                        'strength': '强相关',
                        'score': 8.0,
                        'keywords': ['自组态', '建模方法', '控制系统', '物联网']
                    },
                    {
                        'title': '一种知识融合的方法和装置',
                        'inventors': '田芷瑜; 杨勇; 卫乾; 赵世亮; 许野; 刘斌; 江捷; 徐濮; 王雨婷; 李太帆',
                        'application_date': '2024',
                        'publication_date': '2024-10-15',
                        'relevance': 'Involves knowledge fusion techniques, relevant to multi-modal data integration',
                        'strength': '中等相关',
                        'score': 6.5,
                        'keywords': ['知识融合', '多模态', '数据集成', '智能处理']
                    },
                    {
                        'title': '问答搜索引擎的构建方法和查询业务数据的方法',
                        'inventors': '田芷瑜; 杨勇; 卫乾; 周丽; 吕彬; 李长卓',
                        'application_date': '2024',
                        'publication_date': '2024-06-21',
                        'relevance': 'Builds a Q&A search engine for business data',
                        'strength': '中等相关',
                        'score': 6.0,
                        'keywords': ['问答系统', '搜索引擎', '业务数据', '查询方法']
                    },
                    {
                        'title': '一种用于智能问答的文本解析方法和装置',
                        'inventors': '吕彬; 杨勇; 卫乾; 汪韶雷',
                        'application_date': '2024',
                        'publication_date': '2024-04-12',
                        'relevance': 'Text parsing for intelligent Q&A, directly relevant to language model applications',
                        'strength': '中等相关',
                        'score': 5.5,
                        'keywords': ['文本解析', '智能问答', '语言模型', '自然语言处理']
                    }
                ]
            },
            'C2': {
                'id': 'C2',
                'title': '面向油气生产物联网的时序数据轻量化对象建模与源头质量协同控制方法',
                'short_title': '时序数据轻量化建模与质量控制',
                'patents': [
                    {
                        'title': '一种油气生产智能物联网数据自清洗与修正补齐方法',
                        'inventors': '卫乾; 孙红军; 张明; 邢凯雯',
                        'application_date': '2024',
                        'publication_date': '2024-06-14',
                        'relevance': 'Directly addresses data cleaning and quality control for IoT time-series data',
                        'strength': '强相关',
                        'score': 9.0,
                        'keywords': ['数据清洗', '质量控制', '时序数据', '智能物联网']
                    },
                    {
                        'title': '一种针对VSP处理解释数据的综合质控方法及相关设备',
                        'inventors': '雷刚林; 卫乾; 王腾宇; 杨晓明; 郑多明; 肖又军; 刘胜; 黄录忠; 汪洋; 张振; 蒋震; 段文胜; 王兴军; 王玲; 魏巍; 邓述友',
                        'application_date': '2025',
                        'publication_date': '2025-07-01',
                        'relevance': 'Focuses on comprehensive quality control methods for processing data',
                        'strength': '强相关',
                        'score': 8.5,
                        'keywords': ['质控方法', '数据处理', '综合质控', 'VSP数据']
                    },
                    {
                        'title': '基于边缘计算的抽油机井能耗优化控制方法、装置及系统',
                        'inventors': '宋文容; 卫乾; 孙永光; 张骁; 毛骏; 王伟; 冯志勤; 赵艳江; 樊晶晶',
                        'application_date': '2025',
                        'publication_date': '2025-05-20',
                        'relevance': 'Involves edge computing for real-time control, relevant to source quality control',
                        'strength': '中等相关',
                        'score': 7.0,
                        'keywords': ['边缘计算', '实时控制', '能耗优化', '源头控制']
                    },
                    {
                        'title': '一种基于声纹识别的阀室泄漏检测方法、装置及系统',
                        'inventors': '高飞翔; 卫乾; 赵艳江; 宋文容; 李鹏飞; 庞资胜; 冯志勤; 赵世亮; 龚奕辰; 辛帝萱',
                        'application_date': '2025',
                        'publication_date': '2025-05-16',
                        'relevance': 'Uses acoustic recognition for quality monitoring at the source',
                        'strength': '中等相关',
                        'score': 6.5,
                        'keywords': ['声纹识别', '泄漏检测', '质量监测', '源头监控']
                    },
                    {
                        'title': '油井产量的计量方法、装置、设备及存储介质',
                        'inventors': '赵迎; 石海磊; 卫乾; 刘志海; 徐雷; 刘刚',
                        'application_date': '2025',
                        'publication_date': '2025-04-08',
                        'relevance': 'Involves hybrid modeling for production measurement, relevant to data quality',
                        'strength': '中等相关',
                        'score': 6.0,
                        'keywords': ['产量计量', '混合建模', '数据质量', '生产测量']
                    }
                ]
            },
            'C3': {
                'id': 'C3',
                'title': '油气生产物联网多智能体自组态工作流建模方法',
                'short_title': '多智能体自组态工作流建模',
                'patents': [
                    {
                        'title': '一种用于油气行业的工作流程编排方法及业务流建模方法',
                        'inventors': '吴迪; 王铁成; 王淼; 卫乾; 董杰; 王志伟',
                        'application_date': '2025',
                        'publication_date': '2025-05-30',
                        'relevance': 'Directly addresses workflow modeling and orchestration in oil-gas industry',
                        'strength': '强相关',
                        'score': 9.5,
                        'keywords': ['工作流编排', '业务流建模', '流程管理', '油气行业']
                    },
                    {
                        'title': '一种基于数字孪生数模分离的可视化智能巡检方法和装置',
                        'inventors': '杜秋明; 马凯蒂; 卫乾; 赵艳江; 孙永光; 樊晶晶; 刘刚',
                        'application_date': '2024,2025',
                        'publication_date': '2024-03-26,2025-03-28',
                        'relevance': 'Uses digital twin technology for intelligent inspection workflows',
                        'strength': '强相关',
                        'score': 8.0,
                        'keywords': ['数字孪生', '智能巡检', '可视化', '工作流']
                    },
                    {
                        'title': '一种面向油气生产物联网控制系统的自组态建模方法',
                        'inventors': '卫乾; 孙红军; 张明; 魏靖雯',
                        'application_date': '2024',
                        'publication_date': '2024-07-16',
                        'relevance': 'Focuses specifically on self-configuration modeling for IoT control systems',
                        'strength': '强相关',
                        'score': 8.5,
                        'keywords': ['自组态建模', '控制系统', '物联网', '智能体']
                    },
                    {
                        'title': '基于物联网设备的通信方法、装置、设备和存储介质',
                        'inventors': '马凯蒂; 杨勇; 卫乾; 张铭春; 王庆; 白函司; 李悦明',
                        'application_date': '2024',
                        'publication_date': '2024-06-14',
                        'relevance': 'Addresses communication between IoT devices, relevant to multi-agent systems',
                        'strength': '中等相关',
                        'score': 6.5,
                        'keywords': ['设备通信', '物联网', '多智能体', '通信协议']
                    },
                    {
                        'title': '数据代理装置、方法、电子设备、系统及存储介质',
                        'inventors': '李江; 杨勇; 卫乾; 杜秋明',
                        'application_date': '2023',
                        'publication_date': '2023-03-31,2023-08-22',
                        'relevance': 'Involves data proxy for IoT systems, supporting multi-agent workflows',
                        'strength': '中等相关',
                        'score': 6.0,
                        'keywords': ['数据代理', '系统架构', '工作流支撑', '智能体协作']
                    }
                ]
            }
        }
        
        # 颜色配置
        self.colors = {
            'C1': '#FF6B6B',  # 红色
            'C2': '#4ECDC4',  # 青色
            'C3': '#45B7D1',  # 蓝色
            '强': '#E74C3C',
            '中等': '#F39C12',
            '弱': '#95A5A6',
            'strong': '#E74C3C',
            'medium': '#F39C12',
            'weak': '#95A5A6'
        }
    
    def create_radar_chart(self, problem_id):
        """创建雷达图 - 展示专利在不同技术维度的分布"""
        problem_data = self.scientific_problems[problem_id]
        patents = problem_data['patents']
        
        # 定义技术维度
        dimensions = ['技术创新度', '实用性', '复杂度', '市场价值', '技术成熟度']
        
        fig = go.Figure()
        
        for i, patent in enumerate(patents):
            # 根据专利特点生成雷达图数据
            values = [
                patent['score'],  # 技术创新度
                8.5 if patent['strength'] == '强相关' else 6.0,  # 实用性
                7.0 + i * 0.5,  # 复杂度
                patent['score'] * 0.9,  # 市场价值
                8.0 if '2025' in patent['publication_date'] else 7.0  # 技术成熟度
            ]
            
            fig.add_trace(go.Scatterpolar(
                r=values + [values[0]],  # 闭合雷达图
                theta=dimensions + [dimensions[0]],
                fill='toself',
                name=patent['title'][:20] + '...' if len(patent['title']) > 20 else patent['title'],
                opacity=0.6
            ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 10]
                )),
            showlegend=True,
            title=f"专利技术维度雷达图<br><sub>{problem_data['short_title']}</sub>",
            font=dict(family='Microsoft YaHei', size=12)
        )
        
        return fig

    def create_network_graph(self, problem_id):
        """创建网络图 - 展示专利与科学问题的关系网络"""
        problem_data = self.scientific_problems[problem_id]
        patents = problem_data['patents']

        # 准备节点和边数据
        node_x = []
        node_y = []
        node_text = []
        node_color = []
        node_size = []

        # 中心节点（科学问题）
        node_x.append(0)
        node_y.append(0)
        node_text.append(problem_data['short_title'])
        node_color.append(self.colors[problem_id])
        node_size.append(50)

        # 专利节点
        angles = np.linspace(0, 2*np.pi, len(patents), endpoint=False)
        for i, (patent, angle) in enumerate(zip(patents, angles)):
            radius = 2 if patent['strength'] == '强相关' else 3
            x = radius * np.cos(angle)
            y = radius * np.sin(angle)

            node_x.append(x)
            node_y.append(y)
            node_text.append(patent['title'][:15] + '...')
            node_color.append(self.colors[patent['strength'].replace('相关', '')])
            node_size.append(patent['score'] * 3)

        # 创建边
        edge_x = []
        edge_y = []
        for i in range(1, len(node_x)):
            edge_x.extend([0, node_x[i], None])
            edge_y.extend([0, node_y[i], None])

        # 创建图形
        fig = go.Figure()

        # 添加边
        fig.add_trace(go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=2, color='rgba(125,125,125,0.5)'),
            hoverinfo='none',
            mode='lines',
            showlegend=False
        ))

        # 添加节点
        fig.add_trace(go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            hoverinfo='text',
            text=node_text,
            textposition="middle center",
            marker=dict(
                size=node_size,
                color=node_color,
                line=dict(width=2, color='white')
            ),
            showlegend=False
        ))

        fig.update_layout(
            title=f"专利关系网络图<br><sub>{problem_data['short_title']}</sub>",
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="节点大小表示关联强度，颜色表示关联类型",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color='gray', size=10)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            font=dict(family='Microsoft YaHei', size=10)
        )

        return fig

    def create_sunburst_chart(self, problem_id):
        """创建旭日图 - 展示专利的层次结构和分类"""
        problem_data = self.scientific_problems[problem_id]
        patents = problem_data['patents']

        # 准备数据
        ids = [problem_data['short_title']]
        labels = [problem_data['short_title']]
        parents = ['']
        values = [sum(p['score'] for p in patents)]
        colors = [self.colors[problem_id]]

        # 按强度分组
        strength_groups = {'强相关': [], '中等相关': [], '弱相关': []}
        for patent in patents:
            strength_groups[patent['strength']].append(patent)

        # 添加强度层级
        for strength, group_patents in strength_groups.items():
            if group_patents:
                group_id = f"{problem_data['short_title']}-{strength}"
                ids.append(group_id)
                labels.append(f"{strength} ({len(group_patents)}项)")
                parents.append(problem_data['short_title'])
                values.append(sum(p['score'] for p in group_patents))
                colors.append(self.colors[strength.replace('相关', '')])

                # 添加具体专利
                for patent in group_patents:
                    patent_id = f"{group_id}-{patent['title'][:10]}"
                    ids.append(patent_id)
                    labels.append(patent['title'][:20] + '...')
                    parents.append(group_id)
                    values.append(patent['score'])
                    colors.append(self.colors[strength.replace('相关', '')])

        fig = go.Figure(go.Sunburst(
            ids=ids,
            labels=labels,
            parents=parents,
            values=values,
            branchvalues="total",
            marker=dict(colors=colors),
            hovertemplate='<b>%{label}</b><br>分数: %{value}<extra></extra>',
            maxdepth=3
        ))

        fig.update_layout(
            title=f"专利层次结构旭日图<br><sub>{problem_data['short_title']}</sub>",
            font=dict(family='Microsoft YaHei', size=12)
        )

        return fig

    def create_treemap_chart(self, problem_id):
        """创建树状图 - 展示专利的分层结构和重要性"""
        problem_data = self.scientific_problems[problem_id]
        patents = problem_data['patents']

        # 准备数据
        ids = []
        labels = []
        parents = []
        values = []
        colors = []

        # 根节点
        root_id = problem_data['short_title']
        ids.append(root_id)
        labels.append(problem_data['short_title'])
        parents.append('')
        values.append(0)  # 根节点值为0
        colors.append(self.colors[problem_id])

        # 按强度分组
        strength_groups = {'强相关': [], '中等相关': [], '弱相关': []}
        for patent in patents:
            strength_groups[patent['strength']].append(patent)

        # 添加强度分组
        for strength, group_patents in strength_groups.items():
            if group_patents:
                group_id = f"{strength}"
                ids.append(group_id)
                labels.append(f"{strength}<br>({len(group_patents)}项)")
                parents.append(root_id)
                values.append(sum(p['score'] for p in group_patents))
                colors.append(self.colors[strength.replace('相关', '')])

                # 添加具体专利
                for patent in group_patents:
                    patent_short = patent['title'][:15] + '...' if len(patent['title']) > 15 else patent['title']
                    ids.append(patent['title'])
                    labels.append(f"{patent_short}<br>评分: {patent['score']}")
                    parents.append(group_id)
                    values.append(patent['score'])
                    colors.append(self.colors[strength.replace('相关', '')])

        fig = go.Figure(go.Treemap(
            ids=ids,
            labels=labels,
            parents=parents,
            values=values,
            branchvalues="total",
            marker=dict(colors=colors),
            hovertemplate='<b>%{label}</b><br>分数: %{value}<extra></extra>',
            textinfo="label+value",
            pathbar=dict(visible=True)
        ))

        fig.update_layout(
            title=f"专利重要性树状图<br><sub>{problem_data['short_title']}</sub>",
            font=dict(family='Microsoft YaHei', size=11)
        )

        return fig

    def create_parallel_coordinates(self, problem_id):
        """创建平行坐标图 - 展示专利的多维特征"""
        problem_data = self.scientific_problems[problem_id]
        patents = problem_data['patents']

        # 准备数据
        data = []
        for patent in patents:
            # 计算各种指标
            innovation_score = patent['score']
            complexity = len(patent['keywords'])
            team_size = len(patent['inventors'].split(';'))
            # 处理可能包含多个年份的情况
            app_date = patent['application_date'].split(',')[0] if ',' in patent['application_date'] else patent['application_date']
            recency = 2025 - int(app_date)
            strength_num = 3 if patent['strength'] == '强相关' else 2 if patent['strength'] == '中等相关' else 1

            data.append({
                'patent': patent['title'][:20] + '...',
                'innovation': innovation_score,
                'complexity': complexity,
                'team_size': team_size,
                'recency': recency,
                'strength': strength_num,
                'color': strength_num
            })

        df = pd.DataFrame(data)

        fig = go.Figure(data=
            go.Parcoords(
                line=dict(color=df['color'],
                         colorscale=[[0, '#95A5A6'], [0.5, '#F39C12'], [1, '#E74C3C']],
                         showscale=True,
                         colorbar=dict(title="关联强度")),
                dimensions=list([
                    dict(range=[df['innovation'].min(), df['innovation'].max()],
                         constraintrange=[df['innovation'].min(), df['innovation'].max()],
                         label="创新度", values=df['innovation']),
                    dict(range=[df['complexity'].min(), df['complexity'].max()],
                         label="复杂度", values=df['complexity']),
                    dict(range=[df['team_size'].min(), df['team_size'].max()],
                         label="团队规模", values=df['team_size']),
                    dict(range=[df['recency'].min(), df['recency'].max()],
                         label="时效性", values=df['recency']),
                    dict(range=[1, 3],
                         tickvals=[1, 2, 3],
                         ticktext=['弱相关', '中等相关', '强相关'],
                         label="关联强度", values=df['strength'])
                ])
            )
        )

        fig.update_layout(
            title=f"专利多维特征平行坐标图<br><sub>{problem_data['short_title']}</sub>",
            font=dict(family='Microsoft YaHei', size=12)
        )

        return fig

    def create_comprehensive_dashboard(self, problem_id):
        """创建综合仪表板 - 包含多个图表的综合视图"""
        problem_data = self.scientific_problems[problem_id]

        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('专利分布饼图', '时间线图', '评分对比柱状图', '关键词云图'),
            specs=[[{"type": "pie"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )

        # 1. 饼图 - 专利强度分布
        strength_counts = {}
        for patent in problem_data['patents']:
            strength = patent['strength']
            strength_counts[strength] = strength_counts.get(strength, 0) + 1

        fig.add_trace(
            go.Pie(
                labels=list(strength_counts.keys()),
                values=list(strength_counts.values()),
                marker_colors=[self.colors[s.replace('相关', '')] for s in strength_counts.keys()],
                name="专利分布"
            ),
            row=1, col=1
        )

        # 2. 时间线图 - 专利发布时间
        dates = []
        scores = []
        titles = []
        for patent in problem_data['patents']:
            dates.append(patent['publication_date'])
            scores.append(patent['score'])
            titles.append(patent['title'][:20] + '...')

        fig.add_trace(
            go.Scatter(
                x=dates,
                y=scores,
                mode='markers+lines',
                marker=dict(size=10, color=self.colors[problem_id]),
                text=titles,
                name="时间线"
            ),
            row=1, col=2
        )

        # 3. 柱状图 - 评分对比
        fig.add_trace(
            go.Bar(
                x=[p['title'][:10] + '...' for p in problem_data['patents']],
                y=[p['score'] for p in problem_data['patents']],
                marker_color=[self.colors[p['strength'].replace('相关', '')] for p in problem_data['patents']],
                name="评分对比"
            ),
            row=2, col=1
        )

        # 4. 散点图 - 关键词分析（模拟）
        keyword_counts = {}
        for patent in problem_data['patents']:
            for keyword in patent['keywords']:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1

        keywords = list(keyword_counts.keys())[:10]  # 取前10个关键词
        counts = [keyword_counts[k] for k in keywords]

        fig.add_trace(
            go.Scatter(
                x=keywords,
                y=counts,
                mode='markers',
                marker=dict(
                    size=[c*10 for c in counts],
                    color=self.colors[problem_id],
                    opacity=0.7
                ),
                name="关键词频率"
            ),
            row=2, col=2
        )

        fig.update_layout(
            title=f"专利技术综合分析仪表板<br><sub>{problem_data['short_title']}</sub>",
            showlegend=False,
            font=dict(family='Microsoft YaHei', size=10),
            height=800
        )

        return fig

    def get_enhanced_css(self):
        """获取增强版CSS样式"""
        return """
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }

            .dashboard-container {
                max-width: 1600px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                overflow: hidden;
                backdrop-filter: blur(10px);
            }

            .header-section {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }

            .main-title {
                font-size: 2.5em;
                font-weight: 300;
                margin-bottom: 10px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .subtitle {
                font-size: 1.2em;
                opacity: 0.9;
                font-weight: 300;
            }

            .chart-grid {
                display: grid;
                grid-template-columns: 1fr;
                gap: 30px;
                padding: 40px;
            }

            .chart-card {
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .chart-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            }

            .chart-title {
                font-size: 1.5em;
                color: #2c3e50;
                margin-bottom: 20px;
                text-align: center;
                font-weight: 600;
            }

            .chart-description {
                color: #7f8c8d;
                font-size: 0.9em;
                margin-bottom: 20px;
                text-align: center;
                line-height: 1.5;
            }

            .footer-section {
                background: #ecf0f1;
                padding: 30px 40px;
                text-align: center;
                border-top: 1px solid #bdc3c7;
            }

            .generation-info {
                color: #7f8c8d;
                font-size: 0.9em;
            }

            @media (max-width: 768px) {
                .main-title {
                    font-size: 2em;
                }

                .dashboard-container {
                    margin: 10px;
                    border-radius: 15px;
                }

                .chart-grid {
                    padding: 20px;
                    gap: 20px;
                }
            }
        </style>
        """

    def generate_multi_chart_html(self, problem_id):
        """生成多图表HTML文件"""
        problem_data = self.scientific_problems[problem_id]

        # 创建各种图表
        charts = {
            'radar': self.create_radar_chart(problem_id),
            'network': self.create_network_graph(problem_id),
            'sunburst': self.create_sunburst_chart(problem_id),
            'treemap': self.create_treemap_chart(problem_id),
            'parallel': self.create_parallel_coordinates(problem_id),
            'dashboard': self.create_comprehensive_dashboard(problem_id)
        }

        # 图表描述
        chart_descriptions = {
            'radar': '雷达图展示了每项专利在不同技术维度上的表现，包括技术创新度、实用性、复杂度、市场价值和技术成熟度。',
            'network': '网络图以节点和连线的形式展示专利与科学问题之间的关系网络，节点大小表示关联强度。',
            'sunburst': '旭日图以同心圆的形式展示专利的层次结构，从科学问题到关联强度再到具体专利。',
            'treemap': '树状图通过矩形面积的大小来表示专利的重要性和分层结构，面积越大表示重要性越高。',
            'parallel': '平行坐标图展示专利在多个维度上的特征，每条线代表一项专利，便于比较分析。',
            'dashboard': '综合仪表板集成了多种图表类型，提供专利技术的全方位分析视图。'
        }

        # 图表标题
        chart_titles = {
            'radar': '专利技术维度雷达图',
            'network': '专利关系网络图',
            'sunburst': '专利层次结构旭日图',
            'treemap': '专利重要性树状图',
            'parallel': '专利多维特征平行坐标图',
            'dashboard': '专利技术综合分析仪表板'
        }

        # 生成HTML内容
        css_styles = self.get_enhanced_css()

        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专利技术多维度可视化分析 - {problem_data['short_title']}</title>
    {css_styles}
</head>
<body>
    <div class="dashboard-container">
        <div class="header-section">
            <h1 class="main-title">专利技术多维度可视化分析</h1>
            <p class="subtitle">{problem_data['title']}</p>
        </div>

        <div class="chart-grid">
"""

        # 添加每个图表
        for chart_key, fig in charts.items():
            chart_html = fig.to_html(include_plotlyjs=True, div_id=f"{chart_key}-plot")
            html_content += f"""
            <div class="chart-card">
                <h2 class="chart-title">{chart_titles[chart_key]}</h2>
                <p class="chart-description">{chart_descriptions[chart_key]}</p>
                <div id="{chart_key}-plot">
                    {chart_html}
                </div>
            </div>
"""

        html_content += f"""
        </div>

        <div class="footer-section">
            <div class="generation-info">
                <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                <p><strong>科学问题编号:</strong> {problem_id}</p>
                <p><strong>相关专利数量:</strong> {len(problem_data['patents'])} 项</p>
                <p><strong>可视化图表数量:</strong> {len(charts)} 个</p>
            </div>
        </div>
    </div>
</body>
</html>"""

        return html_content

    def save_multi_chart_html(self, problem_id):
        """保存多图表HTML文件"""
        html_content = self.generate_multi_chart_html(problem_id)
        filename = f'multi_charts_{problem_id}.html'

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"多图表HTML文件已保存: {filename}")
        return filename


def main():
    """主函数"""
    generator = MultiChartGenerator()

    logger.info("开始生成多图表可视化...")

    generated_files = []

    # 为每个科学问题生成多图表可视化
    for problem_id in ['C1', 'C2', 'C3']:
        logger.info(f"正在生成科学问题 {problem_id} 的多图表可视化...")

        try:
            output_file = generator.save_multi_chart_html(problem_id)
            generated_files.append(output_file)

            problem_data = generator.scientific_problems[problem_id]
            print(f"\n=== 科学问题 {problem_id} ===")
            print(f"问题标题: {problem_data['title']}")
            print(f"输出文件: {output_file}")
            print(f"包含图表: 雷达图、网络图、旭日图、树状图、平行坐标图、综合仪表板")

        except Exception as e:
            logger.error(f"生成科学问题 {problem_id} 的多图表可视化时出错: {e}")

    print(f"\n=== 所有多图表可视化生成完成 ===")
    print(f"共生成 {len(generated_files)} 个HTML文件：")
    for filename in generated_files:
        print(f"  - {filename}")

    print(f"\n=== 图表类型说明 ===")
    print("1. 雷达图 - 展示专利在多个技术维度的表现")
    print("2. 网络图 - 展示专利与科学问题的关系网络")
    print("3. 旭日图 - 展示专利的层次结构和分类")
    print("4. 树状图 - 展示专利的重要性和分层结构")
    print("5. 平行坐标图 - 展示专利的多维特征对比")
    print("6. 综合仪表板 - 集成多种图表的综合分析")


if __name__ == "__main__":
    main()
