{"scientific_problems": {"problem1": {"name": "面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究", "keywords": ["物联网", "IoT", "大语言模型", "LLM", "多模态", "协同", "架构", "动态优化", "机器学习", "深度学习", "人工智能", "AI", "自然语言处理", "NLP", "数据融合", "智能分析", "模型优化", "算法优化", "神经网络"]}, "problem2": {"name": "面向油气生产物联网的时序数据轻量化对象建模与源头质量协同控制方法", "keywords": ["时序数据", "轻量化", "对象建模", "质量控制", "数据质量", "源头控制", "数据采集", "数据处理", "数据存储", "数据传输", "边缘计算", "实时数据", "数据压缩", "数据优化", "质控", "监测", "传感器"]}, "problem3": {"name": "油气生产物联网多智能体自组态工作流建模方法", "keywords": ["多智能体", "自组态", "工作流", "建模", "流程编排", "业务流", "智能调度", "自动化", "协作", "分布式", "任务调度", "流程优化", "工作流引擎", "业务建模", "流程管理", "自适应", "动态配置"]}}, "analysis_results": {"problem2": [{"patent_id": "2", "patent_title": "一种油气物联网的管理系统、管理方法、电子设备及介质", "relevance_score": 8.0, "strength": "强相关", "matched_keywords": ["数据采集", "数据处理", "数据传输", "传感器"], "keyword_count": 4}, {"patent_id": "21", "patent_title": "ESummary-主权项: 1.一种面向油气生产物联网控制系统的自组态建模方法,其特征在于,该方法包括：对油气生产现场所监管的各类设备对象和/或生产单元进行抽象,构建相应的油气物模型；通过云组态建模软件搭建可视化组态场景应用,关联绑定相匹配的油气物模型；在油气生产现场部署传感器和控制器,实现以油气物模型为主体的批量实例化对象的设备数据采集、远程传输、实时监测和智能操控的数据联动。", "relevance_score": 7.5, "strength": "强相关", "matched_keywords": ["数据采集", "监测", "传感器"], "keyword_count": 3}, {"patent_id": "20", "patent_title": "一种面向油气生产物联网控制系统的自组态建模方法", "relevance_score": 6.0, "strength": "强相关", "matched_keywords": ["数据采集", "监测", "传感器"], "keyword_count": 3}, {"patent_id": "23", "patent_title": "一种油气生产智能物联网数据自清洗与修正补齐方法", "relevance_score": 6.0, "strength": "强相关", "matched_keywords": ["时序数据", "数据质量", "数据采集"], "keyword_count": 3}, {"patent_id": "13", "patent_title": "Summary-摘要: 本公开实施例提供了一种存储状态数据的方法、装置、设备和存储介质,属于油气勘探开发数据处理技术领域。该方法中,使用数据存储系统对状态数据进行存储,数据存储系统中包括多个边缘存储设备和中心存储设备,这样由多个存储设备分担相应的处理压力,减少单个数据存储设备的处理压力,对于短时大量产生的状态数据可以及时处理,防止状态数据不能快速及时存储,防止状态数据丢失。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["数据处理", "数据存储"], "keyword_count": 2}, {"patent_id": "37", "patent_title": "ESummary-主权项: 1.一种漏油检测的方法,其特征在于,包括：获取目标区域中每个监测位置的采集数据；所述采集数据包括所述监测位置中漏油检测传感器采集的检测数据和图像采集设备采集的视频数据；根据多个检测数据中的异常数据,在多个监测位置中确定候选漏油位置；根据每个所述候选漏油位置的视频数据,在多个候选漏油位置中确定目标漏油位置；根据所述目标漏油位置,进行针对目标区域的漏油报警。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["监测", "传感器"], "keyword_count": 2}, {"patent_id": "49", "patent_title": "Summary-摘要: 1.本外观设计产品的名称：数据处理器(AIoT-sensor)。2.本外观设计产品的用途：用于数据采集和处理。3.本外观设计产品的设计要点：在于形状与图案的结合。4.最能表明设计要点的图片或照片：立体图。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["数据采集", "数据处理"], "keyword_count": 2}, {"patent_id": "1", "patent_title": "一种针对VSP处理解释数据的综合质控方法及相关设备", "relevance_score": 4.5, "strength": "中等相关", "matched_keywords": ["质量控制", "质控"], "keyword_count": 2}, {"patent_id": "36", "patent_title": "一种漏油检测的方法、装置、计算机设备和介质", "relevance_score": 4.0, "strength": "中等相关", "matched_keywords": ["监测", "传感器"], "keyword_count": 2}, {"patent_id": "92", "patent_title": "Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司", "relevance_score": 4.0, "strength": "中等相关", "matched_keywords": ["数据处理", "数据传输"], "keyword_count": 2}, {"patent_id": "99", "patent_title": "Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司", "relevance_score": 4.0, "strength": "中等相关", "matched_keywords": ["数据传输", "传感器"], "keyword_count": 2}, {"patent_id": "6", "patent_title": "基于边缘计算的抽油机井能耗优化控制方法、装置及系统", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["边缘计算"], "keyword_count": 1}, {"patent_id": "31", "patent_title": "数据处理方法、装置、系统、电子设备及可读存储介质", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["数据处理"], "keyword_count": 1}, {"patent_id": "46", "patent_title": "数据处理器(AIoT-sensor)", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["数据处理"], "keyword_count": 1}, {"patent_id": "59", "patent_title": "ESummary-主权项: 1.一种原油含水量分析方法,其特征在于,包括：获取设备实时数据；所述设备实时数据包括稳定塔温度、稳定塔压力、稳前油进换热器压力和空气冷却器温度；将所述设备实时数据输入到原油含水量分析综合模型中,得到原油含水量实时预测值；所述原油含水量分析综合模型由原油含水量预测回归模型和数据判断模型。", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["实时数据"], "keyword_count": 1}, {"patent_id": "90", "patent_title": "数据传输终端及系统", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["数据传输"], "keyword_count": 1}, {"patent_id": "19", "patent_title": "一种图表数据的提取方法和装置", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["传感器"], "keyword_count": 1}, {"patent_id": "34", "patent_title": "一种防爆视频分析设备、方法、电子设备以及存储介质", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["实时数据"], "keyword_count": 1}, {"patent_id": "39", "patent_title": "一种含水量在线分析系统及方法", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["监测"], "keyword_count": 1}, {"patent_id": "45", "patent_title": "一种设备异常的检测系统、方法、计算机设备和介质", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["数据处理"], "keyword_count": 1}, {"patent_id": "54", "patent_title": "一种含水量在线分析系统及方法", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["监测"], "keyword_count": 1}, {"patent_id": "58", "patent_title": "一种原油含水量分析方法及装置", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["实时数据"], "keyword_count": 1}, {"patent_id": "62", "patent_title": "Teacher-申请人: 北京中油瑞飞信息技术有限责任公司", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["数据传输"], "keyword_count": 1}, {"patent_id": "72", "patent_title": "Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["传感器"], "keyword_count": 1}, {"patent_id": "85", "patent_title": "Teacher-申请人: 北京中油瑞飞信息技术有限责任公司;中国石油集团东方地球物理勘探有限责任公司", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["传感器"], "keyword_count": 1}], "problem1": [{"patent_id": "46", "patent_title": "数据处理器(AIoT-sensor)", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["IoT", "AI"], "keyword_count": 2}, {"patent_id": "49", "patent_title": "Summary-摘要: 1.本外观设计产品的名称：数据处理器(AIoT-sensor)。2.本外观设计产品的用途：用于数据采集和处理。3.本外观设计产品的设计要点：在于形状与图案的结合。4.最能表明设计要点的图片或照片：立体图。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["IoT", "AI"], "keyword_count": 2}, {"patent_id": "50", "patent_title": "便携式控制器(RF-AIoT-1000)", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["IoT", "AI"], "keyword_count": 2}, {"patent_id": "53", "patent_title": "Summary-摘要: 1.本外观设计产品的名称：便携式控制器(RF-AIoT-1000)。2.本外观设计产品的用途：用于数据的综合采集和分析,视频流处理。3.本外观设计产品的设计要点：在于形状与图案的结合。4.最能表明设计要点的图片或照片：立体图。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["IoT", "AI"], "keyword_count": 2}, {"patent_id": "2", "patent_title": "一种油气物联网的管理系统、管理方法、电子设备及介质", "relevance_score": 4.5, "strength": "中等相关", "matched_keywords": ["物联网", "数据融合"], "keyword_count": 2}, {"patent_id": "32", "patent_title": "数据代理装置、方法、电子设备、系统及存储介质", "relevance_score": 4.0, "strength": "中等相关", "matched_keywords": ["物联网", "数据融合"], "keyword_count": 2}, {"patent_id": "38", "patent_title": "数据代理装置、方法、电子设备、系统及存储介质", "relevance_score": 4.0, "strength": "中等相关", "matched_keywords": ["物联网", "数据融合"], "keyword_count": 2}, {"patent_id": "20", "patent_title": "一种面向油气生产物联网控制系统的自组态建模方法", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "21", "patent_title": "ESummary-主权项: 1.一种面向油气生产物联网控制系统的自组态建模方法,其特征在于,该方法包括：对油气生产现场所监管的各类设备对象和/或生产单元进行抽象,构建相应的油气物模型；通过云组态建模软件搭建可视化组态场景应用,关联绑定相匹配的油气物模型；在油气生产现场部署传感器和控制器,实现以油气物模型为主体的批量实例化对象的设备数据采集、远程传输、实时监测和智能操控的数据联动。", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "23", "patent_title": "一种油气生产智能物联网数据自清洗与修正补齐方法", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "24", "patent_title": "基于物联网设备的通信方法、装置、设备和存储介质", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "82", "patent_title": "ESummary-主权项: 一种抽油机生产参数优化方法,其特征在于,所述方法包括：获取实时生产参数,所述实时生产参数通过部署在油井现场的物联网设备获取,包括油井工况参数和抽油机生产参数；根据所述实时生产参数,对油井工况进行诊断,获取一个油田中可进行优化的油井,所述油井工况通过部署在作业区生产管理平台上的诊断分析设备进行诊断；根据所述实时生产参数对所述可进行优化的油井的抽油机生产参数进行自动优化。", "relevance_score": 2.5, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "19", "patent_title": "一种图表数据的提取方法和装置", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "63", "patent_title": "一种石油化工生产现场安全合规性实时检测系统及方法", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["深度学习"], "keyword_count": 1}, {"patent_id": "66", "patent_title": "Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}, {"patent_id": "81", "patent_title": "Teacher-申请人: 中国石油集团东方地球物理勘探有限责任公司;北京中油瑞飞信息技术有限责任公司", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["物联网"], "keyword_count": 1}], "problem3": [{"patent_id": "3", "patent_title": "一种用于油气行业的工作流程编排方法及业务流建模方法", "relevance_score": 14.0, "strength": "强相关", "matched_keywords": ["工作流", "建模", "流程编排", "业务流", "协作", "工作流引擎"], "keyword_count": 6}, {"patent_id": "20", "patent_title": "一种面向油气生产物联网控制系统的自组态建模方法", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["自组态", "建模"], "keyword_count": 2}, {"patent_id": "21", "patent_title": "ESummary-主权项: 1.一种面向油气生产物联网控制系统的自组态建模方法,其特征在于,该方法包括：对油气生产现场所监管的各类设备对象和/或生产单元进行抽象,构建相应的油气物模型；通过云组态建模软件搭建可视化组态场景应用,关联绑定相匹配的油气物模型；在油气生产现场部署传感器和控制器,实现以油气物模型为主体的批量实例化对象的设备数据采集、远程传输、实时监测和智能操控的数据联动。", "relevance_score": 5.0, "strength": "中等相关", "matched_keywords": ["自组态", "建模"], "keyword_count": 2}, {"patent_id": "17", "patent_title": "一种基于等值线图的计算等值线间面积方法、装置及设备", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["自动化"], "keyword_count": 1}, {"patent_id": "28", "patent_title": "一种预警方法、装置、电子设备及计算机可读存储介质", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["自动化"], "keyword_count": 1}, {"patent_id": "29", "patent_title": "一种基于等值线图的计算等值线间面积方法、装置及设备", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["自动化"], "keyword_count": 1}, {"patent_id": "55", "patent_title": "一种预警方法、装置、电子设备及计算机可读存储介质", "relevance_score": 2.0, "strength": "弱相关", "matched_keywords": ["自动化"], "keyword_count": 1}]}, "summary": {"problem2": {"problem_name": "面向油气生产物联网的时序数据轻量化对象建模与源头质量协同控制方法", "total_related_patents": 25, "strength_distribution": {"强相关": 4, "中等相关": 7, "弱相关": 14}}, "problem1": {"problem_name": "面向油气生产物联网的多模态大语言模型协同架构与动态优化机制研究", "total_related_patents": 16, "strength_distribution": {"中等相关": 7, "弱相关": 9}}, "problem3": {"problem_name": "油气生产物联网多智能体自组态工作流建模方法", "total_related_patents": 7, "strength_distribution": {"强相关": 1, "中等相关": 2, "弱相关": 4}}}}